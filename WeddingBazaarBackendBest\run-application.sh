#!/bin/bash

# Wedding Bazaar Backend - Application Startup Script
# This script starts all microservices in the correct order

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Configuration
JAVA_OPTS="-Xmx1g -Xms512m"
SPRING_PROFILES_ACTIVE="dev"
LOG_LEVEL="INFO"

# Function to print colored output
print_header() {
    echo -e "${PURPLE}================================${NC}"
    echo -e "${PURPLE}  Wedding Bazaar Backend${NC}"
    echo -e "${PURPLE}  Microservices Startup${NC}"
    echo -e "${PURPLE}================================${NC}"
    echo ""
}

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_step() {
    echo -e "${CYAN}[STEP]${NC} $1"
}

# Function to check if a service is running
check_service() {
    local service_name=$1
    local port=$2
    local max_attempts=30
    local attempt=1
    
    print_status "Checking if $service_name is running on port $port..."
    
    while [ $attempt -le $max_attempts ]; do
        if curl -s -f "http://localhost:$port/actuator/health" > /dev/null 2>&1; then
            print_success "$service_name is running and healthy!"
            return 0
        fi
        
        echo -n "."
        sleep 2
        attempt=$((attempt + 1))
    done
    
    print_error "$service_name failed to start or is not healthy"
    return 1
}

# Function to start a service
start_service() {
    local service_name=$1
    local service_dir=$2
    local port=$3
    local wait_for_health=${4:-true}
    
    print_step "Starting $service_name..."
    
    cd "$service_dir"
    
    # Start the service in background
    nohup mvn spring-boot:run \
        -Dspring-boot.run.jvmArguments="$JAVA_OPTS" \
        -Dspring.profiles.active="$SPRING_PROFILES_ACTIVE" \
        -Dlogging.level.root="$LOG_LEVEL" \
        > "../logs/${service_name}.log" 2>&1 &
    
    local pid=$!
    echo $pid > "../logs/${service_name}.pid"
    
    print_status "$service_name started with PID $pid"
    
    # Wait for service to be healthy
    if [ "$wait_for_health" = true ]; then
        if check_service "$service_name" "$port"; then
            print_success "$service_name is ready!"
        else
            print_error "$service_name failed to start properly"
            return 1
        fi
    else
        print_status "$service_name started (not waiting for health check)"
        sleep 5
    fi
    
    cd ..
}

# Function to check prerequisites
check_prerequisites() {
    print_step "Checking prerequisites..."
    
    # Check Java
    if ! command -v java &> /dev/null; then
        print_error "Java is not installed or not in PATH"
        exit 1
    fi
    
    local java_version=$(java -version 2>&1 | head -n 1 | cut -d'"' -f2 | cut -d'.' -f1)
    if [ "$java_version" -lt 17 ]; then
        print_error "Java 17 or higher is required. Current version: $java_version"
        exit 1
    fi
    print_success "Java $java_version detected"
    
    # Check Maven
    if ! command -v mvn &> /dev/null; then
        print_error "Maven is not installed or not in PATH"
        exit 1
    fi
    print_success "Maven detected"
    
    # Check if ports are available
    local ports=(8761 8888 8080 8081 8082 8083 8084 8085)
    for port in "${ports[@]}"; do
        if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
            print_warning "Port $port is already in use"
        fi
    done
}

# Function to setup infrastructure
setup_infrastructure() {
    print_step "Setting up infrastructure services..."
    
    # Check if Docker is available
    if command -v docker &> /dev/null && command -v docker-compose &> /dev/null; then
        print_status "Starting infrastructure with Docker Compose..."
        
        # Start infrastructure services
        docker-compose -f docker-compose.dev.yml up -d postgres mongodb elasticsearch redis kafka zookeeper
        
        print_status "Waiting for infrastructure services to be ready..."
        sleep 30
        
        print_success "Infrastructure services started"
    else
        print_warning "Docker not available. Please ensure the following services are running:"
        echo "  - PostgreSQL (port 5432)"
        echo "  - MongoDB (port 27017)"
        echo "  - Elasticsearch (port 9200)"
        echo "  - Redis (port 6379)"
        echo "  - Kafka (port 9092)"
        echo ""
        read -p "Press Enter to continue when services are ready..."
    fi
}

# Function to build the project
build_project() {
    print_step "Building the project..."
    
    mvn clean install -DskipTests -T 4
    
    if [ $? -eq 0 ]; then
        print_success "Project built successfully"
    else
        print_error "Project build failed"
        exit 1
    fi
}

# Function to create log directory
setup_logging() {
    print_step "Setting up logging..."
    
    mkdir -p logs
    
    # Clear old logs
    rm -f logs/*.log
    rm -f logs/*.pid
    
    print_success "Logging setup complete"
}

# Function to start all services
start_all_services() {
    print_step "Starting microservices in order..."
    
    # 1. Service Registry (Eureka)
    start_service "Service Registry" "service-registry" 8761
    
    # 2. Config Server
    start_service "Config Server" "config-server" 8888
    
    # 3. API Gateway
    start_service "API Gateway" "api-gateway" 8080
    
    # 4. Core Services
    start_service "User Service" "user-service" 8081
    start_service "Vendor Service" "vendor-service" 8082
    start_service "Search Service" "search-service" 8083
    start_service "Booking Service" "booking-service" 8084
    start_service "Payment Service" "payment-service" 8085
}

# Function to show service status
show_status() {
    print_step "Service Status Summary:"
    echo ""
    
    local services=(
        "Service Registry:8761"
        "Config Server:8888"
        "API Gateway:8080"
        "User Service:8081"
        "Vendor Service:8082"
        "Search Service:8083"
        "Booking Service:8084"
        "Payment Service:8085"
    )
    
    for service_info in "${services[@]}"; do
        local service_name=$(echo $service_info | cut -d':' -f1)
        local port=$(echo $service_info | cut -d':' -f2)
        
        if curl -s -f "http://localhost:$port/actuator/health" > /dev/null 2>&1; then
            print_success "$service_name - Running (http://localhost:$port)"
        else
            print_error "$service_name - Not Running"
        fi
    done
    
    echo ""
    print_status "API Documentation:"
    echo "  - Swagger UI: http://localhost:8080/swagger-ui.html"
    echo "  - Eureka Dashboard: http://localhost:8761"
    echo "  - Config Server: http://localhost:8888"
    echo ""
}

# Function to stop all services
stop_services() {
    print_step "Stopping all services..."
    
    # Kill services using PID files
    for pid_file in logs/*.pid; do
        if [ -f "$pid_file" ]; then
            local pid=$(cat "$pid_file")
            local service_name=$(basename "$pid_file" .pid)
            
            if kill -0 "$pid" 2>/dev/null; then
                print_status "Stopping $service_name (PID: $pid)"
                kill "$pid"
                rm -f "$pid_file"
            fi
        fi
    done
    
    print_success "All services stopped"
}

# Function to show help
show_help() {
    echo "Usage: $0 [COMMAND]"
    echo ""
    echo "Commands:"
    echo "  start     Start all services (default)"
    echo "  stop      Stop all services"
    echo "  restart   Restart all services"
    echo "  status    Show service status"
    echo "  build     Build the project only"
    echo "  help      Show this help message"
    echo ""
    echo "Environment Variables:"
    echo "  SPRING_PROFILES_ACTIVE  Spring profiles (default: dev)"
    echo "  LOG_LEVEL              Logging level (default: INFO)"
    echo "  JAVA_OPTS              JVM options (default: -Xmx1g -Xms512m)"
    echo ""
}

# Main execution
main() {
    local command=${1:-start}
    
    case $command in
        start)
            print_header
            check_prerequisites
            setup_logging
            setup_infrastructure
            build_project
            start_all_services
            show_status
            
            print_success "Wedding Bazaar Backend is now running!"
            print_status "Press Ctrl+C to stop all services"
            
            # Wait for interrupt
            trap stop_services INT
            while true; do
                sleep 1
            done
            ;;
        stop)
            stop_services
            ;;
        restart)
            stop_services
            sleep 2
            $0 start
            ;;
        status)
            show_status
            ;;
        build)
            check_prerequisites
            build_project
            ;;
        help)
            show_help
            ;;
        *)
            print_error "Unknown command: $command"
            show_help
            exit 1
            ;;
    esac
}

# Run main function
main "$@"
