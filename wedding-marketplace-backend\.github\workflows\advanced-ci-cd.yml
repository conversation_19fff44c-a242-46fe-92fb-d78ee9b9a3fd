name: Advanced CI/CD Pipeline

on:
  push:
    branches: [ main, develop, 'feature/*', 'release/*' ]
  pull_request:
    branches: [ main, develop ]
  release:
    types: [ published ]

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}
  JAVA_VERSION: '17'
  MAVEN_OPTS: '-Xmx3072m'

jobs:
  # Code Quality and Security Analysis
  code-quality:
    name: Code Quality & Security
    runs-on: ubuntu-latest
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        fetch-depth: 0

    - name: Set up JDK ${{ env.JAVA_VERSION }}
      uses: actions/setup-java@v3
      with:
        java-version: ${{ env.JAVA_VERSION }}
        distribution: 'temurin'

    - name: Cache Maven dependencies
      uses: actions/cache@v3
      with:
        path: ~/.m2
        key: ${{ runner.os }}-m2-${{ hashFiles('**/pom.xml') }}
        restore-keys: ${{ runner.os }}-m2

    - name: Run SpotBugs analysis
      run: mvn spotbugs:check

    - name: Run OWASP Dependency Check
      run: mvn org.owasp:dependency-check-maven:check

    - name: SonarCloud Scan
      uses: SonarSource/sonarcloud-github-action@master
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}

    - name: CodeQL Analysis
      uses: github/codeql-action/init@v2
      with:
        languages: java

    - name: Autobuild
      uses: github/codeql-action/autobuild@v2

    - name: Perform CodeQL Analysis
      uses: github/codeql-action/analyze@v2

  # Unit and Integration Tests
  test:
    name: Test Suite
    runs-on: ubuntu-latest
    strategy:
      matrix:
        profile: [unit, integration, contract]
    services:
      mysql:
        image: mysql:8.0
        env:
          MYSQL_ROOT_PASSWORD: rootpassword
          MYSQL_DATABASE: wedding_marketplace_test
        ports:
          - 3306:3306
        options: --health-cmd="mysqladmin ping" --health-interval=10s --health-timeout=5s --health-retries=3

      redis:
        image: redis:7-alpine
        ports:
          - 6379:6379
        options: --health-cmd="redis-cli ping" --health-interval=10s --health-timeout=5s --health-retries=3

      kafka:
        image: confluentinc/cp-kafka:latest
        env:
          KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
          KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://localhost:9092
          KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
        ports:
          - 9092:9092

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up JDK ${{ env.JAVA_VERSION }}
      uses: actions/setup-java@v3
      with:
        java-version: ${{ env.JAVA_VERSION }}
        distribution: 'temurin'

    - name: Cache Maven dependencies
      uses: actions/cache@v3
      with:
        path: ~/.m2
        key: ${{ runner.os }}-m2-${{ hashFiles('**/pom.xml') }}
        restore-keys: ${{ runner.os }}-m2

    - name: Run ${{ matrix.profile }} tests
      run: mvn test -P${{ matrix.profile }} -Dspring.profiles.active=test

    - name: Generate test report
      uses: dorny/test-reporter@v1
      if: success() || failure()
      with:
        name: ${{ matrix.profile }} Test Results
        path: target/surefire-reports/*.xml
        reporter: java-junit

    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: target/site/jacoco/jacoco.xml
        flags: ${{ matrix.profile }}

  # Build and Package
  build:
    name: Build & Package
    runs-on: ubuntu-latest
    needs: [code-quality, test]
    outputs:
      image-digest: ${{ steps.build.outputs.digest }}
      image-tag: ${{ steps.meta.outputs.tags }}
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up JDK ${{ env.JAVA_VERSION }}
      uses: actions/setup-java@v3
      with:
        java-version: ${{ env.JAVA_VERSION }}
        distribution: 'temurin'

    - name: Cache Maven dependencies
      uses: actions/cache@v3
      with:
        path: ~/.m2
        key: ${{ runner.os }}-m2-${{ hashFiles('**/pom.xml') }}
        restore-keys: ${{ runner.os }}-m2

    - name: Build application
      run: mvn clean package -DskipTests -Dspring.profiles.active=prod

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3

    - name: Log in to Container Registry
      uses: docker/login-action@v3
      with:
        registry: ${{ env.REGISTRY }}
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}

    - name: Extract metadata
      id: meta
      uses: docker/metadata-action@v5
      with:
        images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}
        tags: |
          type=ref,event=branch
          type=ref,event=pr
          type=semver,pattern={{version}}
          type=semver,pattern={{major}}.{{minor}}
          type=sha,prefix={{branch}}-

    - name: Build and push Docker image
      id: build
      uses: docker/build-push-action@v5
      with:
        context: .
        platforms: linux/amd64,linux/arm64
        push: true
        tags: ${{ steps.meta.outputs.tags }}
        labels: ${{ steps.meta.outputs.labels }}
        cache-from: type=gha
        cache-to: type=gha,mode=max

    - name: Generate SBOM
      uses: anchore/sbom-action@v0
      with:
        image: ${{ steps.meta.outputs.tags }}
        format: spdx-json
        output-file: sbom.spdx.json

    - name: Scan image for vulnerabilities
      uses: anchore/scan-action@v3
      with:
        image: ${{ steps.meta.outputs.tags }}
        fail-build: true
        severity-cutoff: high

  # Security Scanning
  security:
    name: Security Scan
    runs-on: ubuntu-latest
    needs: build
    steps:
    - name: Run Trivy vulnerability scanner
      uses: aquasecurity/trivy-action@master
      with:
        image-ref: ${{ needs.build.outputs.image-tag }}
        format: 'sarif'
        output: 'trivy-results.sarif'

    - name: Upload Trivy scan results to GitHub Security tab
      uses: github/codeql-action/upload-sarif@v2
      with:
        sarif_file: 'trivy-results.sarif'

  # Deploy to Development
  deploy-dev:
    name: Deploy to Development
    runs-on: ubuntu-latest
    needs: [build, security]
    if: github.ref == 'refs/heads/develop'
    environment:
      name: development
      url: https://dev-api.wedding-marketplace.com
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Configure AWS credentials
      uses: aws-actions/configure-aws-credentials@v4
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        aws-region: us-east-1

    - name: Update kubeconfig
      run: aws eks update-kubeconfig --name wedding-marketplace-dev

    - name: Deploy with Helm
      run: |
        helm upgrade --install wedding-marketplace-dev ./helm/wedding-marketplace \
          --namespace wedding-marketplace-dev \
          --create-namespace \
          --set image.tag=${{ github.sha }} \
          --set environment=development \
          --values ./helm/wedding-marketplace/values-dev.yaml \
          --wait --timeout=10m

    - name: Run smoke tests
      run: |
        kubectl wait --for=condition=ready pod -l app=wedding-marketplace-backend -n wedding-marketplace-dev --timeout=300s
        curl -f https://dev-api.wedding-marketplace.com/actuator/health

  # Deploy to Staging
  deploy-staging:
    name: Deploy to Staging
    runs-on: ubuntu-latest
    needs: [build, security]
    if: github.ref == 'refs/heads/main'
    environment:
      name: staging
      url: https://staging-api.wedding-marketplace.com
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Configure AWS credentials
      uses: aws-actions/configure-aws-credentials@v4
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        aws-region: us-east-1

    - name: Update kubeconfig
      run: aws eks update-kubeconfig --name wedding-marketplace-staging

    - name: Deploy with Helm (Canary)
      run: |
        helm upgrade --install wedding-marketplace-staging ./helm/wedding-marketplace \
          --namespace wedding-marketplace-staging \
          --create-namespace \
          --set image.tag=${{ github.sha }} \
          --set environment=staging \
          --set deployment.replicaCount=2 \
          --set canary.enabled=true \
          --set canary.weight=10 \
          --values ./helm/wedding-marketplace/values-staging.yaml \
          --wait --timeout=10m

    - name: Run integration tests
      run: |
        kubectl wait --for=condition=ready pod -l app=wedding-marketplace-backend -n wedding-marketplace-staging --timeout=300s
        npm run test:integration -- --baseUrl=https://staging-api.wedding-marketplace.com

    - name: Promote canary deployment
      if: success()
      run: |
        helm upgrade wedding-marketplace-staging ./helm/wedding-marketplace \
          --namespace wedding-marketplace-staging \
          --set canary.weight=100 \
          --reuse-values \
          --wait --timeout=5m

  # Deploy to Production
  deploy-prod:
    name: Deploy to Production
    runs-on: ubuntu-latest
    needs: [deploy-staging]
    if: github.event_name == 'release'
    environment:
      name: production
      url: https://api.wedding-marketplace.com
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Configure AWS credentials
      uses: aws-actions/configure-aws-credentials@v4
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        aws-region: us-east-1

    - name: Update kubeconfig
      run: aws eks update-kubeconfig --name wedding-marketplace-prod

    - name: Deploy with Helm (Blue-Green)
      run: |
        helm upgrade --install wedding-marketplace-prod ./helm/wedding-marketplace \
          --namespace wedding-marketplace-prod \
          --create-namespace \
          --set image.tag=${{ github.event.release.tag_name }} \
          --set environment=production \
          --set deployment.strategy.type=BlueGreen \
          --values ./helm/wedding-marketplace/values-prod.yaml \
          --wait --timeout=15m

    - name: Run production health checks
      run: |
        kubectl wait --for=condition=ready pod -l app=wedding-marketplace-backend -n wedding-marketplace-prod --timeout=600s
        curl -f https://api.wedding-marketplace.com/actuator/health

    - name: Notify deployment success
      uses: 8398a7/action-slack@v3
      with:
        status: success
        text: "🚀 Wedding Marketplace Backend ${{ github.event.release.tag_name }} deployed to production successfully!"
      env:
        SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
