# Global configuration for all services
spring:
  datasource:
    driver-class-name: org.postgresql.Driver
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      idle-timeout: 300000
      connection-timeout: 20000
      leak-detection-threshold: 60000
  jpa:
    hibernate:
      ddl-auto: validate
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        format_sql: true
        show_sql: false
        jdbc:
          batch_size: 20
        order_inserts: true
        order_updates: true
    open-in-view: false
  flyway:
    enabled: true
    baseline-on-migrate: true
    validate-on-migrate: true
  redis:
    host: localhost
    port: 6379
    timeout: 2000ms
    lettuce:
      pool:
        max-active: 8
        max-idle: 8
        min-idle: 0
  kafka:
    bootstrap-servers: localhost:9092
    producer:
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.springframework.kafka.support.serializer.JsonSerializer
      acks: all
      retries: 3
      batch-size: 16384
      linger-ms: 1
      buffer-memory: 33554432
    consumer:
      group-id: wedding-bazaar
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      value-deserializer: org.springframework.kafka.support.serializer.JsonDeserializer
      auto-offset-reset: earliest
      enable-auto-commit: false
      properties:
        spring.json.trusted.packages: "com.wedding.*"

# JWT Configuration
jwt:
  secret: wedding-bazaar-secret-key-2024-production
  expiration: 86400 # 24 hours
  refresh-expiration: 604800 # 7 days
  issuer: wedding-bazaar

# OAuth2 Configuration
oauth2:
  google:
    client-id: ${GOOGLE_CLIENT_ID:your-google-client-id}
    client-secret: ${GOOGLE_CLIENT_SECRET:your-google-client-secret}
  facebook:
    client-id: ${FACEBOOK_CLIENT_ID:your-facebook-client-id}
    client-secret: ${FACEBOOK_CLIENT_SECRET:your-facebook-client-secret}

# Payment Gateway Configuration
payment:
  stripe:
    public-key: ${STRIPE_PUBLIC_KEY:pk_test_your_stripe_public_key}
    secret-key: ${STRIPE_SECRET_KEY:sk_test_your_stripe_secret_key}
    webhook-secret: ${STRIPE_WEBHOOK_SECRET:whsec_your_webhook_secret}
  paypal:
    client-id: ${PAYPAL_CLIENT_ID:your-paypal-client-id}
    client-secret: ${PAYPAL_CLIENT_SECRET:your-paypal-client-secret}
    mode: sandbox # sandbox or live

# Email Configuration
mail:
  host: smtp.gmail.com
  port: 587
  username: ${MAIL_USERNAME:<EMAIL>}
  password: ${MAIL_PASSWORD:your-app-password}
  properties:
    mail:
      smtp:
        auth: true
        starttls:
          enable: true

# File Upload Configuration
file:
  upload:
    max-size: 10MB
    allowed-types: jpg,jpeg,png,pdf,doc,docx
    base-path: ${FILE_UPLOAD_PATH:/uploads}

# Elasticsearch Configuration
elasticsearch:
  host: localhost
  port: 9200
  username: ${ELASTICSEARCH_USERNAME:}
  password: ${ELASTICSEARCH_PASSWORD:}

# MongoDB Configuration
mongodb:
  host: localhost
  port: 27017
  database: wedding_bazaar
  username: ${MONGODB_USERNAME:}
  password: ${MONGODB_PASSWORD:}

# Eureka Configuration
eureka:
  client:
    service-url:
      defaultZone: ************************************/eureka/
    fetch-registry: true
    register-with-eureka: true
  instance:
    prefer-ip-address: true
    lease-renewal-interval-in-seconds: 30
    lease-expiration-duration-in-seconds: 90

# Management and Monitoring
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always
  metrics:
    export:
      prometheus:
        enabled: true

# Logging Configuration
logging:
  level:
    com.wedding: DEBUG
    org.springframework.security: INFO
    org.springframework.web: INFO
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"

# Application Configuration
app:
  name: Wedding Bazaar
  version: 1.0.0
  description: Wedding Marketplace Platform
  frontend-url: ${FRONTEND_URL:http://localhost:3000}
  backend-url: ${BACKEND_URL:http://localhost:8080}
  support-email: <EMAIL>
  admin-email: <EMAIL>
