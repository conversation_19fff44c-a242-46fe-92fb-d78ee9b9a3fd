package com.weddingbazaar.booking.entity;

import com.weddingbazaar.common.enums.BookingStatus;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * Booking entity
 */
@Entity
@Table(name = "bookings")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EntityListeners(AuditingEntityListener.class)
public class Booking {
    
    @Id
    @GeneratedValue(strategy = GenerationType.UUID)
    private String id;
    
    @Column(name = "user_id", nullable = false)
    private String userId;
    
    @Column(name = "vendor_id", nullable = false)
    private String vendorId;
    
    @Column(name = "service_id", nullable = false)
    private String serviceId;
    
    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private BookingStatus status = BookingStatus.PENDING;
    
    @Column(name = "event_date", nullable = false)
    private LocalDateTime eventDate;
    
    @Column(name = "event_end_date")
    private LocalDateTime eventEndDate;
    
    @Column(name = "event_location", nullable = false)
    private String eventLocation;
    
    @Column(name = "event_type")
    private String eventType;
    
    @Column(name = "guest_count")
    private Integer guestCount;
    
    @Column(name = "total_amount", nullable = false)
    private BigDecimal totalAmount;
    
    @Column(name = "advance_amount")
    private BigDecimal advanceAmount;
    
    @Column(name = "remaining_amount")
    private BigDecimal remainingAmount;
    
    @Column(name = "commission_amount")
    private BigDecimal commissionAmount;
    
    @Column(name = "vendor_amount")
    private BigDecimal vendorAmount;
    
    @Column(name = "currency")
    private String currency = "INR";
    
    @Enumerated(EnumType.STRING)
    @Column(name = "payment_status")
    private PaymentStatus paymentStatus = PaymentStatus.PENDING;
    
    @Column(name = "special_requirements", columnDefinition = "TEXT")
    private String specialRequirements;
    
    @Column(name = "vendor_notes", columnDefinition = "TEXT")
    private String vendorNotes;
    
    @Column(name = "admin_notes", columnDefinition = "TEXT")
    private String adminNotes;
    
    @Column(name = "cancellation_reason")
    private String cancellationReason;
    
    @Column(name = "cancelled_by")
    private String cancelledBy; // USER, VENDOR, ADMIN
    
    @Column(name = "cancelled_at")
    private LocalDateTime cancelledAt;
    
    @Column(name = "confirmed_at")
    private LocalDateTime confirmedAt;
    
    @Column(name = "completed_at")
    private LocalDateTime completedAt;
    
    @Column(name = "refund_amount")
    private BigDecimal refundAmount = BigDecimal.ZERO;
    
    @Column(name = "refund_processed_at")
    private LocalDateTime refundProcessedAt;
    
    @OneToMany(mappedBy = "booking", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<BookingStatusHistory> statusHistory;
    
    @CreatedDate
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;
    
    @LastModifiedDate
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
    
    public enum PaymentStatus {
        PENDING,
        ADVANCE_PAID,
        FULLY_PAID,
        REFUNDED,
        PARTIALLY_REFUNDED,
        FAILED
    }
}
