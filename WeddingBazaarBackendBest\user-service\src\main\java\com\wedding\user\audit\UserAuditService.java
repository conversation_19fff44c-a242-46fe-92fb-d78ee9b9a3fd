package com.wedding.user.audit;

import com.wedding.user.model.User;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * Advanced audit service for comprehensive user activity tracking
 * Implements audit trail, compliance logging, and forensic capabilities
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class UserAuditService {
    
    private final UserAuditRepository auditRepository;
    private final ObjectMapper objectMapper;
    
    /**
     * Creates a snapshot of user state for audit purposes
     */
    public UserSnapshot createSnapshot(User user) {
        return UserSnapshot.builder()
                .userId(user.getId())
                .email(user.getEmail())
                .firstName(user.getFirstName())
                .lastName(user.getLastName())
                .phoneNumber(user.getPhoneNumber())
                .dateOfBirth(user.getDateOfBirth())
                .city(user.getCity())
                .state(user.getState())
                .country(user.getCountry())
                .isActive(user.getIsActive())
                .isVerified(user.getIsVerified())
                .profileImageUrl(user.getProfileImageUrl())
                .lastLogin(user.getLastLogin())
                .loginCount(user.getLoginCount())
                .failedLoginAttempts(user.getFailedLoginAttempts())
                .lockedUntil(user.getLockedUntil())
                .createdAt(user.getCreatedAt())
                .updatedAt(user.getUpdatedAt())
                .snapshotTime(LocalDateTime.now())
                .build();
    }
    
    /**
     * Logs user registration with comprehensive details
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void logUserRegistration(User user, String registrationSource) {
        Map<String, Object> details = new HashMap<>();
        details.put("registrationSource", registrationSource);
        details.put("userAgent", getCurrentUserAgent());
        details.put("ipAddress", getCurrentIpAddress());
        details.put("timestamp", LocalDateTime.now());
        
        UserAuditLog auditLog = createAuditLog(
                user.getId(),
                user.getEmail(),
                AuditAction.USER_REGISTERED,
                "User registered successfully",
                details,
                null,
                createSnapshot(user)
        );
        
        auditRepository.save(auditLog);
        log.info("Audit: User registration logged for user: {}", user.getId());
    }
    
    /**
     * Logs failed user registration attempts
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void logUserRegistrationFailure(String email, String reason) {
        Map<String, Object> details = new HashMap<>();
        details.put("email", email);
        details.put("failureReason", reason);
        details.put("userAgent", getCurrentUserAgent());
        details.put("ipAddress", getCurrentIpAddress());
        details.put("timestamp", LocalDateTime.now());
        
        UserAuditLog auditLog = createAuditLog(
                null,
                email,
                AuditAction.USER_REGISTRATION_FAILED,
                "User registration failed: " + reason,
                details,
                null,
                null
        );
        
        auditRepository.save(auditLog);
        log.warn("Audit: User registration failure logged for email: {}", email);
    }
    
    /**
     * Logs email verification events
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void logEmailVerification(User user) {
        Map<String, Object> details = new HashMap<>();
        details.put("verificationMethod", "EMAIL_LINK");
        details.put("ipAddress", getCurrentIpAddress());
        details.put("userAgent", getCurrentUserAgent());
        
        UserAuditLog auditLog = createAuditLog(
                user.getId(),
                user.getEmail(),
                AuditAction.EMAIL_VERIFIED,
                "Email verified successfully",
                details,
                null,
                createSnapshot(user)
        );
        
        auditRepository.save(auditLog);
        log.info("Audit: Email verification logged for user: {}", user.getId());
    }
    
    /**
     * Logs password change events
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void logPasswordChange(User user) {
        Map<String, Object> details = new HashMap<>();
        details.put("changeMethod", "USER_INITIATED");
        details.put("ipAddress", getCurrentIpAddress());
        details.put("userAgent", getCurrentUserAgent());
        details.put("previousPasswordAge", calculatePasswordAge(user));
        
        UserAuditLog auditLog = createAuditLog(
                user.getId(),
                user.getEmail(),
                AuditAction.PASSWORD_CHANGED,
                "Password changed successfully",
                details,
                null,
                createSnapshot(user)
        );
        
        auditRepository.save(auditLog);
        log.info("Audit: Password change logged for user: {}", user.getId());
    }
    
    /**
     * Logs failed password change attempts
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void logPasswordChangeFailure(User user, String reason) {
        Map<String, Object> details = new HashMap<>();
        details.put("failureReason", reason);
        details.put("ipAddress", getCurrentIpAddress());
        details.put("userAgent", getCurrentUserAgent());
        
        UserAuditLog auditLog = createAuditLog(
                user.getId(),
                user.getEmail(),
                AuditAction.PASSWORD_CHANGE_FAILED,
                "Password change failed: " + reason,
                details,
                null,
                null
        );
        
        auditRepository.save(auditLog);
        log.warn("Audit: Password change failure logged for user: {}", user.getId());
    }
    
    /**
     * Logs user login events
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void logUserLogin(User user, boolean successful, String failureReason) {
        Map<String, Object> details = new HashMap<>();
        details.put("successful", successful);
        details.put("ipAddress", getCurrentIpAddress());
        details.put("userAgent", getCurrentUserAgent());
        details.put("deviceInfo", getDeviceInfo());
        details.put("location", getLocationInfo());
        
        if (!successful) {
            details.put("failureReason", failureReason);
            details.put("attemptCount", user.getFailedLoginAttempts());
        }
        
        AuditAction action = successful ? AuditAction.USER_LOGIN_SUCCESS : AuditAction.USER_LOGIN_FAILED;
        String description = successful ? "User logged in successfully" : "User login failed: " + failureReason;
        
        UserAuditLog auditLog = createAuditLog(
                user.getId(),
                user.getEmail(),
                action,
                description,
                details,
                null,
                successful ? createSnapshot(user) : null
        );
        
        auditRepository.save(auditLog);
        
        if (successful) {
            log.info("Audit: Successful login logged for user: {}", user.getId());
        } else {
            log.warn("Audit: Failed login logged for user: {}", user.getId());
        }
    }
    
    /**
     * Logs user logout events
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void logUserLogout(User user, String sessionId, String logoutReason) {
        Map<String, Object> details = new HashMap<>();
        details.put("sessionId", sessionId);
        details.put("logoutReason", logoutReason);
        details.put("sessionDuration", calculateSessionDuration(sessionId));
        
        UserAuditLog auditLog = createAuditLog(
                user.getId(),
                user.getEmail(),
                AuditAction.USER_LOGOUT,
                "User logged out",
                details,
                null,
                null
        );
        
        auditRepository.save(auditLog);
        log.info("Audit: User logout logged for user: {}", user.getId());
    }
    
    /**
     * Logs user account locking
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void logUserLocked(User user, String reason) {
        Map<String, Object> details = new HashMap<>();
        details.put("lockReason", reason);
        details.put("lockedBy", getCurrentUserId());
        details.put("lockDuration", user.getLockedUntil());
        details.put("failedAttempts", user.getFailedLoginAttempts());
        
        UserAuditLog auditLog = createAuditLog(
                user.getId(),
                user.getEmail(),
                AuditAction.USER_LOCKED,
                "User account locked: " + reason,
                details,
                null,
                createSnapshot(user)
        );
        
        auditRepository.save(auditLog);
        log.warn("Audit: User account locked for user: {} - Reason: {}", user.getId(), reason);
    }
    
    /**
     * Logs data access events for compliance
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void logDataAccess(String userId, String dataType, String accessReason, String accessedBy) {
        Map<String, Object> details = new HashMap<>();
        details.put("dataType", dataType);
        details.put("accessReason", accessReason);
        details.put("accessedBy", accessedBy);
        details.put("ipAddress", getCurrentIpAddress());
        details.put("userAgent", getCurrentUserAgent());
        
        UserAuditLog auditLog = createAuditLog(
                userId,
                null,
                AuditAction.DATA_ACCESSED,
                "User data accessed: " + dataType,
                details,
                null,
                null
        );
        
        auditRepository.save(auditLog);
        log.info("Audit: Data access logged for user: {} - Type: {}", userId, dataType);
    }
    
    /**
     * Logs GDPR compliance events
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void logGdprEvent(String userId, String email, GdprEventType eventType, Map<String, Object> eventDetails) {
        Map<String, Object> details = new HashMap<>(eventDetails);
        details.put("gdprEventType", eventType);
        details.put("complianceOfficer", getCurrentUserId());
        details.put("legalBasis", eventDetails.get("legalBasis"));
        
        UserAuditLog auditLog = createAuditLog(
                userId,
                email,
                AuditAction.GDPR_EVENT,
                "GDPR event: " + eventType,
                details,
                null,
                null
        );
        
        auditRepository.save(auditLog);
        log.info("Audit: GDPR event logged for user: {} - Type: {}", userId, eventType);
    }
    
    /**
     * Retrieves audit trail for a user
     */
    public Page<UserAuditLog> getAuditTrail(String userId, Pageable pageable) {
        return auditRepository.findByUserIdOrderByTimestampDesc(userId, pageable);
    }
    
    /**
     * Retrieves audit logs by action type
     */
    public Page<UserAuditLog> getAuditLogsByAction(AuditAction action, Pageable pageable) {
        return auditRepository.findByActionOrderByTimestampDesc(action, pageable);
    }
    
    /**
     * Searches audit logs with filters
     */
    public Page<UserAuditLog> searchAuditLogs(AuditSearchCriteria criteria, Pageable pageable) {
        return auditRepository.findByCriteria(criteria, pageable);
    }
    
    /**
     * Gets audit statistics for compliance reporting
     */
    public Map<String, Object> getAuditStatistics(LocalDateTime from, LocalDateTime to) {
        Map<String, Object> stats = new HashMap<>();
        
        stats.put("totalEvents", auditRepository.countByTimestampBetween(from, to));
        stats.put("loginAttempts", auditRepository.countByActionAndTimestampBetween(AuditAction.USER_LOGIN_SUCCESS, from, to));
        stats.put("failedLogins", auditRepository.countByActionAndTimestampBetween(AuditAction.USER_LOGIN_FAILED, from, to));
        stats.put("registrations", auditRepository.countByActionAndTimestampBetween(AuditAction.USER_REGISTERED, from, to));
        stats.put("passwordChanges", auditRepository.countByActionAndTimestampBetween(AuditAction.PASSWORD_CHANGED, from, to));
        stats.put("accountLocks", auditRepository.countByActionAndTimestampBetween(AuditAction.USER_LOCKED, from, to));
        stats.put("dataAccess", auditRepository.countByActionAndTimestampBetween(AuditAction.DATA_ACCESSED, from, to));
        stats.put("gdprEvents", auditRepository.countByActionAndTimestampBetween(AuditAction.GDPR_EVENT, from, to));
        
        return stats;
    }
    
    // Private helper methods
    
    private UserAuditLog createAuditLog(String userId, String email, AuditAction action, String description,
                                       Map<String, Object> details, UserSnapshot beforeSnapshot, UserSnapshot afterSnapshot) {
        try {
            return UserAuditLog.builder()
                    .userId(userId)
                    .email(email)
                    .action(action)
                    .description(description)
                    .details(objectMapper.writeValueAsString(details))
                    .beforeSnapshot(beforeSnapshot != null ? objectMapper.writeValueAsString(beforeSnapshot) : null)
                    .afterSnapshot(afterSnapshot != null ? objectMapper.writeValueAsString(afterSnapshot) : null)
                    .ipAddress(getCurrentIpAddress())
                    .userAgent(getCurrentUserAgent())
                    .performedBy(getCurrentUserId())
                    .timestamp(LocalDateTime.now())
                    .build();
        } catch (JsonProcessingException e) {
            log.error("Failed to serialize audit log details", e);
            throw new RuntimeException("Failed to create audit log", e);
        }
    }
    
    private String getCurrentUserAgent() {
        // Implementation would get from request context
        return "UserAgent"; // Placeholder
    }
    
    private String getCurrentIpAddress() {
        // Implementation would get from request context
        return "127.0.0.1"; // Placeholder
    }
    
    private String getCurrentUserId() {
        // Implementation would get from security context
        return "system"; // Placeholder
    }
    
    private String getDeviceInfo() {
        // Implementation would parse user agent for device info
        return "Unknown Device"; // Placeholder
    }
    
    private String getLocationInfo() {
        // Implementation would use IP geolocation
        return "Unknown Location"; // Placeholder
    }
    
    private long calculatePasswordAge(User user) {
        // Implementation would calculate days since last password change
        return 0; // Placeholder
    }
    
    private long calculateSessionDuration(String sessionId) {
        // Implementation would calculate session duration
        return 0; // Placeholder
    }
    
    // Enums and supporting classes
    
    public enum GdprEventType {
        DATA_EXPORT_REQUESTED,
        DATA_EXPORT_COMPLETED,
        DATA_DELETION_REQUESTED,
        DATA_DELETION_COMPLETED,
        CONSENT_GRANTED,
        CONSENT_WITHDRAWN,
        DATA_RECTIFICATION,
        DATA_PORTABILITY
    }
}
