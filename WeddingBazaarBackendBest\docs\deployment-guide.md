# Wedding Bazaar Backend - Deployment Guide

## 🚀 Production Deployment

### Prerequisites
- **Kubernetes Cluster** (EKS, GKE, or AKS)
- **Docker Registry** (ECR, GCR, or Docker Hub)
- **Helm 3.x**
- **kubectl** configured for your cluster

### 1. Build and Push Docker Images

```bash
# Build all services
./scripts/build-docker-images.sh

# Push to registry
./scripts/push-docker-images.sh
```

### 2. Deploy Infrastructure Services

```bash
# Deploy PostgreSQL
helm install postgres bitnami/postgresql \
  --set auth.postgresPassword=your-password \
  --set primary.persistence.size=100Gi

# Deploy MongoDB
helm install mongodb bitnami/mongodb \
  --set auth.rootPassword=your-password \
  --set persistence.size=100Gi

# Deploy Redis
helm install redis bitnami/redis \
  --set auth.password=your-password

# Deploy Elasticsearch
helm install elasticsearch elastic/elasticsearch \
  --set replicas=3 \
  --set volumeClaimTemplate.resources.requests.storage=100Gi

# Deploy Ka<PERSON><PERSON>
helm install kafka bitnami/kafka \
  --set persistence.size=100Gi
```

### 3. Deploy Application Services

```bash
# Deploy using Helm charts
helm install wedding-bazaar ./helm/wedding-marketplace \
  --set global.registry=your-registry \
  --set global.tag=latest \
  --values values-production.yaml
```

## 🔧 Configuration Management

### Environment-Specific Configurations

#### Development (values-dev.yaml)
```yaml
global:
  environment: development
  registry: localhost:5000
  tag: dev

replicaCount: 1

resources:
  limits:
    cpu: 500m
    memory: 512Mi
  requests:
    cpu: 250m
    memory: 256Mi

database:
  host: postgres-dev
  name: wedding_bazaar_dev
```

#### Production (values-prod.yaml)
```yaml
global:
  environment: production
  registry: your-prod-registry
  tag: v1.0.0

replicaCount: 3

resources:
  limits:
    cpu: 2000m
    memory: 2Gi
  requests:
    cpu: 1000m
    memory: 1Gi

database:
  host: postgres-prod
  name: wedding_bazaar_prod

autoscaling:
  enabled: true
  minReplicas: 3
  maxReplicas: 10
  targetCPUUtilizationPercentage: 70
```

## 📊 Monitoring and Observability

### Prometheus Configuration
```yaml
# prometheus-values.yaml
server:
  persistentVolume:
    size: 100Gi
  
  global:
    scrape_interval: 15s
    evaluation_interval: 15s

alertmanager:
  persistentVolume:
    size: 10Gi

grafana:
  adminPassword: your-secure-password
  persistence:
    enabled: true
    size: 10Gi
```

### Deploy Monitoring Stack
```bash
# Add Prometheus Helm repo
helm repo add prometheus-community https://prometheus-community.github.io/helm-charts

# Install Prometheus + Grafana
helm install monitoring prometheus-community/kube-prometheus-stack \
  --values prometheus-values.yaml
```

## 🔐 Security Configuration

### SSL/TLS Setup
```yaml
# ingress-tls.yaml
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: wedding-bazaar-ingress
  annotations:
    cert-manager.io/cluster-issuer: letsencrypt-prod
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
spec:
  tls:
  - hosts:
    - api.weddingbazaar.com
    secretName: wedding-bazaar-tls
  rules:
  - host: api.weddingbazaar.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: api-gateway
            port:
              number: 8080
```

### Network Policies
```yaml
# network-policy.yaml
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: wedding-bazaar-network-policy
spec:
  podSelector:
    matchLabels:
      app: wedding-bazaar
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from:
    - podSelector:
        matchLabels:
          app: nginx-ingress
    ports:
    - protocol: TCP
      port: 8080
```

## 🔄 CI/CD Pipeline

### GitHub Actions Workflow
```yaml
# .github/workflows/deploy.yml
name: Deploy to Production

on:
  push:
    branches: [main]

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up JDK 17
      uses: actions/setup-java@v3
      with:
        java-version: '17'
        distribution: 'temurin'
    
    - name: Build with Maven
      run: mvn clean package -DskipTests
    
    - name: Build Docker images
      run: ./scripts/build-docker-images.sh
    
    - name: Push to registry
      run: ./scripts/push-docker-images.sh
    
    - name: Deploy to Kubernetes
      run: |
        helm upgrade --install wedding-bazaar ./helm/wedding-marketplace \
          --set global.tag=${{ github.sha }} \
          --values values-production.yaml
```

## 📈 Scaling and Performance

### Horizontal Pod Autoscaler
```yaml
# hpa.yaml
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: wedding-bazaar-hpa
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: user-service
  minReplicas: 3
  maxReplicas: 20
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
```

### Database Optimization
```sql
-- Create indexes for better performance
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_vendors_category_city ON vendors(category, city);
CREATE INDEX idx_bookings_user_id ON bookings(user_id);
CREATE INDEX idx_bookings_vendor_id ON bookings(vendor_id);
CREATE INDEX idx_bookings_event_date ON bookings(event_date);
CREATE INDEX idx_payments_booking_id ON payments(booking_id);
CREATE INDEX idx_payments_status ON payments(payment_status);

-- Partitioning for large tables
CREATE TABLE bookings_2024 PARTITION OF bookings
FOR VALUES FROM ('2024-01-01') TO ('2025-01-01');
```

## 🔧 Troubleshooting

### Common Issues

#### Service Discovery Issues
```bash
# Check Eureka registration
kubectl logs -f deployment/eureka-server

# Check service registration
curl http://eureka-server:8761/eureka/apps
```

#### Database Connection Issues
```bash
# Check database connectivity
kubectl exec -it deployment/user-service -- nc -zv postgres 5432

# Check database logs
kubectl logs -f deployment/postgres
```

#### Performance Issues
```bash
# Check resource usage
kubectl top pods

# Check application metrics
curl http://user-service:8081/actuator/metrics
```

### Health Checks
```bash
# Check all services health
kubectl get pods -l app=wedding-bazaar

# Check specific service health
kubectl exec -it deployment/user-service -- curl localhost:8081/actuator/health
```

## 📋 Maintenance

### Backup Strategy
```bash
# Database backup
kubectl exec -it postgres-0 -- pg_dump -U postgres wedding_bazaar > backup.sql

# MongoDB backup
kubectl exec -it mongodb-0 -- mongodump --db wedding_bazaar --out /backup

# Elasticsearch backup
curl -X PUT "elasticsearch:9200/_snapshot/backup_repo/snapshot_1"
```

### Rolling Updates
```bash
# Update specific service
helm upgrade wedding-bazaar ./helm/wedding-marketplace \
  --set userService.image.tag=v1.1.0 \
  --reuse-values

# Rollback if needed
helm rollback wedding-bazaar 1
```

This deployment guide provides comprehensive instructions for deploying the Wedding Bazaar backend in production environments with proper monitoring, security, and scaling configurations.
