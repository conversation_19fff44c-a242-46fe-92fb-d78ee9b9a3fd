package com.wedding.vendor.ml;

import com.wedding.vendor.dto.VendorRecommendationRequest;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Supporting classes for the ML recommendation engine
 */

/**
 * Context object for recommendation generation
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RecommendationContext {
    private String userId;
    private VendorRecommendationRequest request;
    private int maxResults = 20;
    private Map<String, Object> additionalContext;
    
    public RecommendationContext(String userId, VendorRecommendationRequest request) {
        this.userId = userId;
        this.request = request;
        this.additionalContext = new HashMap<>();
    }
}

/**
 * Represents a recommendation result with score and source
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RecommendationResult {
    private String vendorId;
    private double score;
    private String source;
    private Map<String, Object> metadata;
    
    public RecommendationResult(String vendorId, double score, String source) {
        this.vendorId = vendorId;
        this.score = score;
        this.source = source;
        this.metadata = new HashMap<>();
    }
}

/**
 * User behavior profile for personalized recommendations
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserBehaviorProfile {
    private String userId;
    private Map<String, Double> vendorPreferences; // vendorId -> preference score
    private Map<String, Double> categoryPreferences; // category -> preference score
    private Map<String, Double> featurePreferences; // feature -> preference score
    private Map<String, Double> priceRangePreferences; // price range -> preference score
    private Map<String, Double> locationPreferences; // location -> preference score
    private int interactionCount;
    private int bookingCount;
    private double averageRating;
    private LocalDateTime lastActivity;
    private List<String> searchHistory;
    private List<String> viewHistory;
    private List<String> bookingHistory;
    
    public UserBehaviorProfile(String userId) {
        this.userId = userId;
        this.vendorPreferences = new HashMap<>();
        this.categoryPreferences = new HashMap<>();
        this.featurePreferences = new HashMap<>();
        this.priceRangePreferences = new HashMap<>();
        this.locationPreferences = new HashMap<>();
        this.searchHistory = new ArrayList<>();
        this.viewHistory = new ArrayList<>();
        this.bookingHistory = new ArrayList<>();
    }
}

/**
 * Analyzes user behavior to build preference profiles
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class UserBehaviorAnalyzer {
    
    private final UserInteractionRepository interactionRepository;
    private final VendorBookingRepository bookingRepository;
    
    /**
     * Analyzes user behavior and builds preference profile
     */
    public UserBehaviorProfile analyzeUserBehavior(String userId) {
        log.debug("Analyzing behavior for user: {}", userId);
        
        UserBehaviorProfile profile = new UserBehaviorProfile(userId);
        
        // Analyze search patterns
        analyzeSearchBehavior(userId, profile);
        
        // Analyze view patterns
        analyzeViewBehavior(userId, profile);
        
        // Analyze booking patterns
        analyzeBookingBehavior(userId, profile);
        
        // Analyze rating patterns
        analyzeRatingBehavior(userId, profile);
        
        // Calculate derived preferences
        calculateDerivedPreferences(profile);
        
        log.debug("Behavior analysis completed for user: {} - {} interactions", 
                userId, profile.getInteractionCount());
        
        return profile;
    }
    
    /**
     * Finds users with similar behavior patterns
     */
    public List<String> findSimilarUsers(String userId, int maxUsers) {
        UserBehaviorProfile userProfile = analyzeUserBehavior(userId);
        
        // Get candidate users who have interacted with similar vendors
        Set<String> candidateUsers = new HashSet<>();
        for (String vendorId : userProfile.getVendorPreferences().keySet()) {
            List<String> usersWhoLikedVendor = interactionRepository.findUsersWhoInteractedWithVendor(vendorId);
            candidateUsers.addAll(usersWhoLikedVendor);
        }
        
        // Calculate similarity scores
        Map<String, Double> similarityScores = new HashMap<>();
        for (String candidateUserId : candidateUsers) {
            if (!candidateUserId.equals(userId)) {
                UserBehaviorProfile candidateProfile = analyzeUserBehavior(candidateUserId);
                double similarity = calculateBehaviorSimilarity(userProfile, candidateProfile);
                if (similarity > 0.1) { // Minimum similarity threshold
                    similarityScores.put(candidateUserId, similarity);
                }
            }
        }
        
        // Return top similar users
        return similarityScores.entrySet().stream()
                .sorted(Map.Entry.<String, Double>comparingByValue().reversed())
                .limit(maxUsers)
                .map(Map.Entry::getKey)
                .collect(Collectors.toList());
    }
    
    private void analyzeSearchBehavior(String userId, UserBehaviorProfile profile) {
        List<UserSearchEvent> searchEvents = interactionRepository.findUserSearchEvents(userId);
        
        for (UserSearchEvent event : searchEvents) {
            // Update category preferences
            if (event.getCategory() != null) {
                profile.getCategoryPreferences().merge(event.getCategory(), 0.1, Double::sum);
            }
            
            // Update location preferences
            if (event.getLocation() != null) {
                profile.getLocationPreferences().merge(event.getLocation(), 0.1, Double::sum);
            }
            
            // Update price range preferences
            if (event.getPriceRange() != null) {
                profile.getPriceRangePreferences().merge(event.getPriceRange(), 0.1, Double::sum);
            }
            
            profile.getSearchHistory().add(event.getSearchQuery());
        }
        
        profile.setInteractionCount(profile.getInteractionCount() + searchEvents.size());
    }
    
    private void analyzeViewBehavior(String userId, UserBehaviorProfile profile) {
        List<VendorViewEvent> viewEvents = interactionRepository.findUserViewEvents(userId);
        
        for (VendorViewEvent event : viewEvents) {
            // Higher weight for longer view durations
            double weight = calculateViewWeight(event.getViewDurationSeconds());
            
            profile.getVendorPreferences().merge(event.getVendorId(), weight, Double::sum);
            profile.getViewHistory().add(event.getVendorId());
        }
        
        profile.setInteractionCount(profile.getInteractionCount() + viewEvents.size());
    }
    
    private void analyzeBookingBehavior(String userId, UserBehaviorProfile profile) {
        List<VendorBookingEvent> bookingEvents = bookingRepository.findUserBookingEvents(userId);
        
        for (VendorBookingEvent event : bookingEvents) {
            // High weight for actual bookings
            double weight = 2.0;
            
            // Extra weight for completed bookings
            if ("COMPLETED".equals(event.getStatus())) {
                weight = 3.0;
            }
            
            profile.getVendorPreferences().merge(event.getVendorId(), weight, Double::sum);
            profile.getBookingHistory().add(event.getVendorId());
        }
        
        profile.setBookingCount(bookingEvents.size());
        profile.setInteractionCount(profile.getInteractionCount() + bookingEvents.size());
    }
    
    private void analyzeRatingBehavior(String userId, UserBehaviorProfile profile) {
        List<VendorRatingEvent> ratingEvents = interactionRepository.findUserRatingEvents(userId);
        
        double totalRating = 0.0;
        int ratingCount = 0;
        
        for (VendorRatingEvent event : ratingEvents) {
            // Weight based on rating value
            double weight = event.getRating() / 5.0;
            
            profile.getVendorPreferences().merge(event.getVendorId(), weight, Double::sum);
            
            totalRating += event.getRating();
            ratingCount++;
        }
        
        if (ratingCount > 0) {
            profile.setAverageRating(totalRating / ratingCount);
        }
        
        profile.setInteractionCount(profile.getInteractionCount() + ratingEvents.size());
    }
    
    private void calculateDerivedPreferences(UserBehaviorProfile profile) {
        // Normalize vendor preferences
        double maxVendorScore = profile.getVendorPreferences().values().stream()
                .mapToDouble(Double::doubleValue)
                .max()
                .orElse(1.0);
        
        if (maxVendorScore > 0) {
            profile.getVendorPreferences().replaceAll((k, v) -> v / maxVendorScore);
        }
        
        // Calculate feature preferences based on vendor interactions
        calculateFeaturePreferences(profile);
        
        // Set last activity
        profile.setLastActivity(LocalDateTime.now());
    }
    
    private void calculateFeaturePreferences(UserBehaviorProfile profile) {
        // This would analyze the features of vendors the user has interacted with
        // and derive feature preferences
        
        Map<String, Double> featureScores = new HashMap<>();
        
        for (Map.Entry<String, Double> entry : profile.getVendorPreferences().entrySet()) {
            String vendorId = entry.getKey();
            double preference = entry.getValue();
            
            // Get vendor features and weight them by user preference
            List<String> vendorFeatures = getVendorFeatures(vendorId);
            for (String feature : vendorFeatures) {
                featureScores.merge(feature, preference, Double::sum);
            }
        }
        
        profile.setFeaturePreferences(featureScores);
    }
    
    private double calculateBehaviorSimilarity(UserBehaviorProfile profile1, UserBehaviorProfile profile2) {
        // Calculate similarity based on multiple factors
        double vendorSimilarity = calculateVendorPreferenceSimilarity(profile1, profile2);
        double categorySimilarity = calculateCategoryPreferenceSimilarity(profile1, profile2);
        double priceSimilarity = calculatePricePreferenceSimilarity(profile1, profile2);
        
        // Weighted combination
        return 0.5 * vendorSimilarity + 0.3 * categorySimilarity + 0.2 * priceSimilarity;
    }
    
    private double calculateVendorPreferenceSimilarity(UserBehaviorProfile profile1, UserBehaviorProfile profile2) {
        Map<String, Double> prefs1 = profile1.getVendorPreferences();
        Map<String, Double> prefs2 = profile2.getVendorPreferences();
        
        Set<String> commonVendors = new HashSet<>(prefs1.keySet());
        commonVendors.retainAll(prefs2.keySet());
        
        if (commonVendors.isEmpty()) {
            return 0.0;
        }
        
        double dotProduct = 0.0;
        double norm1 = 0.0;
        double norm2 = 0.0;
        
        for (String vendorId : commonVendors) {
            double pref1 = prefs1.get(vendorId);
            double pref2 = prefs2.get(vendorId);
            
            dotProduct += pref1 * pref2;
            norm1 += pref1 * pref1;
            norm2 += pref2 * pref2;
        }
        
        if (norm1 == 0.0 || norm2 == 0.0) {
            return 0.0;
        }
        
        return dotProduct / (Math.sqrt(norm1) * Math.sqrt(norm2));
    }
    
    private double calculateCategoryPreferenceSimilarity(UserBehaviorProfile profile1, UserBehaviorProfile profile2) {
        // Similar calculation for category preferences
        return calculatePreferenceSimilarity(profile1.getCategoryPreferences(), profile2.getCategoryPreferences());
    }
    
    private double calculatePricePreferenceSimilarity(UserBehaviorProfile profile1, UserBehaviorProfile profile2) {
        // Similar calculation for price preferences
        return calculatePreferenceSimilarity(profile1.getPriceRangePreferences(), profile2.getPriceRangePreferences());
    }
    
    private double calculatePreferenceSimilarity(Map<String, Double> prefs1, Map<String, Double> prefs2) {
        Set<String> commonKeys = new HashSet<>(prefs1.keySet());
        commonKeys.retainAll(prefs2.keySet());
        
        if (commonKeys.isEmpty()) {
            return 0.0;
        }
        
        double dotProduct = 0.0;
        double norm1 = 0.0;
        double norm2 = 0.0;
        
        for (String key : commonKeys) {
            double pref1 = prefs1.get(key);
            double pref2 = prefs2.get(key);
            
            dotProduct += pref1 * pref2;
            norm1 += pref1 * pref1;
            norm2 += pref2 * pref2;
        }
        
        if (norm1 == 0.0 || norm2 == 0.0) {
            return 0.0;
        }
        
        return dotProduct / (Math.sqrt(norm1) * Math.sqrt(norm2));
    }
    
    private double calculateViewWeight(long viewDurationSeconds) {
        // Weight based on view duration
        if (viewDurationSeconds < 10) return 0.1;
        if (viewDurationSeconds < 30) return 0.3;
        if (viewDurationSeconds < 60) return 0.5;
        if (viewDurationSeconds < 180) return 0.7;
        return 1.0;
    }
    
    private List<String> getVendorFeatures(String vendorId) {
        // Implementation would fetch vendor features from database
        return Collections.emptyList(); // Placeholder
    }
}

// Event classes for user interactions
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
class UserSearchEvent {
    private String userId;
    private String searchQuery;
    private String category;
    private String location;
    private String priceRange;
    private LocalDateTime timestamp;
}

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
class VendorViewEvent {
    private String userId;
    private String vendorId;
    private long viewDurationSeconds;
    private LocalDateTime timestamp;
}

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
class VendorBookingEvent {
    private String userId;
    private String vendorId;
    private String status;
    private LocalDateTime timestamp;
}

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
class VendorRatingEvent {
    private String userId;
    private String vendorId;
    private double rating;
    private LocalDateTime timestamp;
}

// Repository interfaces (would be implemented separately)
interface UserInteractionRepository {
    List<UserSearchEvent> findUserSearchEvents(String userId);
    List<VendorViewEvent> findUserViewEvents(String userId);
    List<VendorRatingEvent> findUserRatingEvents(String userId);
    List<String> findUsersWhoInteractedWithVendor(String vendorId);
}

interface VendorBookingRepository {
    List<VendorBookingEvent> findUserBookingEvents(String userId);
}
