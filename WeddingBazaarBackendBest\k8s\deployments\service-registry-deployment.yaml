apiVersion: apps/v1
kind: Deployment
metadata:
  name: service-registry
  namespace: wedding-bazaar
  labels:
    app: service-registry
    version: v1
spec:
  replicas: 1
  selector:
    matchLabels:
      app: service-registry
  template:
    metadata:
      labels:
        app: service-registry
        version: v1
    spec:
      containers:
      - name: service-registry
        image: wedding-bazaar/service-registry:latest
        ports:
        - containerPort: 8761
          name: http
        env:
        - name: SPRING_PROFILES_ACTIVE
          value: "kubernetes"
        - name: EUREKA_INSTANCE_HOSTNAME
          valueFrom:
            fieldRef:
              fieldPath: metadata.name
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /actuator/health
            port: 8761
          initialDelaySeconds: 60
          periodSeconds: 30
          timeoutSeconds: 10
        readinessProbe:
          httpGet:
            path: /actuator/health
            port: 8761
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
---
apiVersion: v1
kind: Service
metadata:
  name: service-registry
  namespace: wedding-bazaar
  labels:
    app: service-registry
spec:
  type: ClusterIP
  ports:
  - port: 8761
    targetPort: 8761
    name: http
  selector:
    app: service-registry
