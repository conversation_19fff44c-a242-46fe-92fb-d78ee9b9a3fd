{"properties": {"id": {"type": "keyword"}, "vendorId": {"type": "keyword"}, "businessName": {"type": "text", "analyzer": "standard_analyzer", "search_analyzer": "standard_analyzer", "fields": {"keyword": {"type": "keyword", "normalizer": "lowercase_normalizer"}, "autocomplete": {"type": "text", "analyzer": "autocomplete_analyzer", "search_analyzer": "search_analyzer"}}}, "shortDescription": {"type": "text", "analyzer": "standard_analyzer"}, "description": {"type": "text", "analyzer": "standard_analyzer"}, "category": {"type": "keyword"}, "subcategories": {"type": "keyword"}, "city": {"type": "keyword", "fields": {"text": {"type": "text", "analyzer": "standard_analyzer"}}}, "state": {"type": "keyword"}, "country": {"type": "keyword"}, "pincode": {"type": "keyword"}, "fullAddress": {"type": "text", "analyzer": "standard_analyzer"}, "location": {"type": "geo_point"}, "ratingAverage": {"type": "double"}, "totalReviews": {"type": "integer"}, "totalBookings": {"type": "integer"}, "completedBookings": {"type": "integer"}, "completionRate": {"type": "double"}, "isVerified": {"type": "boolean"}, "isActive": {"type": "boolean"}, "isFeatured": {"type": "boolean"}, "isPremium": {"type": "boolean"}, "verificationStatus": {"type": "keyword"}, "startingPrice": {"type": "double"}, "maxPrice": {"type": "double"}, "yearsOfExperience": {"type": "integer"}, "teamSize": {"type": "integer"}, "serviceAreas": {"type": "text", "analyzer": "standard_analyzer", "fields": {"keyword": {"type": "keyword"}}}, "specializations": {"type": "text", "analyzer": "standard_analyzer", "fields": {"keyword": {"type": "keyword"}}}, "languages": {"type": "keyword"}, "responseTimeHours": {"type": "integer"}, "advanceBookingDays": {"type": "integer"}, "profileImageUrl": {"type": "keyword", "index": false}, "galleryImages": {"type": "keyword", "index": false}, "coverImageUrl": {"type": "keyword", "index": false}, "subscriptionPlan": {"type": "keyword"}, "lastActive": {"type": "date"}, "createdAt": {"type": "date"}, "updatedAt": {"type": "date"}, "services": {"type": "nested", "properties": {"id": {"type": "keyword"}, "name": {"type": "text", "analyzer": "standard_analyzer", "fields": {"keyword": {"type": "keyword"}}}, "category": {"type": "keyword"}, "subcategory": {"type": "keyword"}, "basePrice": {"type": "double"}, "maxPrice": {"type": "double"}, "priceUnit": {"type": "keyword"}, "isActive": {"type": "boolean"}, "isFeatured": {"type": "boolean"}, "features": {"type": "text", "analyzer": "standard_analyzer", "fields": {"keyword": {"type": "keyword"}}}, "tags": {"type": "keyword"}, "ratingAverage": {"type": "double"}, "reviewCount": {"type": "integer"}, "bookingCount": {"type": "integer"}}}, "socialMetrics": {"type": "object", "properties": {"facebookFollowers": {"type": "integer"}, "instagramFollowers": {"type": "integer"}, "youtubeSubscribers": {"type": "integer"}, "engagementRate": {"type": "double"}, "lastUpdated": {"type": "date"}}}, "businessHours": {"type": "object", "enabled": false}, "suggest": {"type": "completion", "analyzer": "standard_analyzer", "preserve_separators": true, "preserve_position_increments": true, "max_input_length": 50, "contexts": [{"name": "category", "type": "category"}, {"name": "city", "type": "category"}]}}}