# Multi-stage build for Vendor Service
FROM openjdk:17-jdk-slim as builder

WORKDIR /app

COPY .mvn/ .mvn/
COPY mvnw pom.xml ./
COPY shared-library/pom.xml shared-library/
COPY vendor-service/pom.xml vendor-service/

RUN ./mvnw dependency:go-offline -B

COPY shared-library/src shared-library/src/
COPY vendor-service/src vendor-service/src/

RUN ./mvnw clean package -DskipTests -pl vendor-service -am

# Runtime stage
FROM openjdk:17-jre-slim

RUN apt-get update && apt-get install -y curl && rm -rf /var/lib/apt/lists/*
RUN groupadd -r appuser && useradd -r -g appuser appuser

WORKDIR /app

COPY --from=builder /app/vendor-service/target/vendor-service-*.jar app.jar
RUN chown -R appuser:appuser /app

USER appuser
EXPOSE 8082

HEALTHCHECK --interval=30s --timeout=3s --start-period=60s --retries=3 \
  CMD curl -f http://localhost:8082/actuator/health || exit 1

ENTRYPOINT ["java", "-XX:+UseContainerSupport", "-XX:MaxRAMPercentage=75.0", "-jar", "app.jar"]
