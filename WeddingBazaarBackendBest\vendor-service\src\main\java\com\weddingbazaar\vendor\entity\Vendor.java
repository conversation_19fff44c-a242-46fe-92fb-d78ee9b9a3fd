package com.weddingbazaar.vendor.entity;

import com.weddingbazaar.common.enums.VendorCategory;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * Vendor entity for PostgreSQL
 */
@Entity
@Table(name = "vendors")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EntityListeners(AuditingEntityListener.class)
public class Vendor {
    
    @Id
    @GeneratedValue(strategy = GenerationType.UUID)
    private String id;
    
    @Column(name = "user_id", nullable = false)
    private String userId; // Reference to User service
    
    @Column(name = "business_name", nullable = false)
    private String businessName;
    
    @Column(name = "business_email")
    private String businessEmail;
    
    @Column(name = "business_phone")
    private String businessPhone;
    
    @Column(name = "gst_number")
    private String gstNumber;
    
    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private VendorCategory category;
    
    @Column(nullable = false)
    private String city;
    
    @Column(nullable = false)
    private String state;
    
    private String country = "India";
    
    @Column(name = "pincode")
    private String pincode;
    
    @Column(name = "full_address")
    private String fullAddress;
    
    @Column(name = "rating_average")
    private BigDecimal ratingAverage = BigDecimal.ZERO;
    
    @Column(name = "total_reviews")
    private Integer totalReviews = 0;
    
    @Column(name = "total_bookings")
    private Integer totalBookings = 0;
    
    @Column(name = "is_verified")
    private Boolean isVerified = false;
    
    @Column(name = "is_active")
    private Boolean isActive = true;
    
    @Column(name = "is_featured")
    private Boolean isFeatured = false;
    
    @Column(name = "verification_status")
    @Enumerated(EnumType.STRING)
    private VerificationStatus verificationStatus = VerificationStatus.PENDING;
    
    @Column(name = "profile_image_url")
    private String profileImageUrl;
    
    @ElementCollection
    @CollectionTable(name = "vendor_gallery", joinColumns = @JoinColumn(name = "vendor_id"))
    @Column(name = "image_url")
    private List<String> galleryImages;
    
    @Column(name = "description", columnDefinition = "TEXT")
    private String description;
    
    @Column(name = "website_url")
    private String websiteUrl;
    
    @Column(name = "facebook_url")
    private String facebookUrl;
    
    @Column(name = "instagram_url")
    private String instagramUrl;
    
    @Column(name = "years_of_experience")
    private Integer yearsOfExperience;
    
    @Column(name = "starting_price")
    private BigDecimal startingPrice;
    
    @Column(name = "max_price")
    private BigDecimal maxPrice;
    
    @Column(name = "commission_rate")
    private BigDecimal commissionRate = new BigDecimal("10.00"); // 10% default
    
    @CreatedDate
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;
    
    @LastModifiedDate
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
    
    public enum VerificationStatus {
        PENDING, VERIFIED, REJECTED, SUSPENDED
    }
}
