package com.weddingbazaar.search.document;

import com.weddingbazaar.common.enums.VendorCategory;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.elasticsearch.annotations.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * Elasticsearch document for vendor search
 */
@Document(indexName = "vendors")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class VendorSearchDocument {
    
    @Id
    private String id;
    
    private String vendorId;
    
    @Field(type = FieldType.Text, analyzer = "standard")
    private String businessName;
    
    @Field(type = FieldType.Keyword)
    private VendorCategory category;
    
    @Field(type = FieldType.Text, analyzer = "standard")
    private String description;
    
    @Field(type = FieldType.Keyword)
    private String city;
    
    @Field(type = FieldType.Keyword)
    private String state;
    
    @Field(type = FieldType.Keyword)
    private String country;
    
    @Field(type = FieldType.Text)
    private String pincode;
    
    @GeoPointField
    private String location; // "lat,lon" format
    
    @Field(type = FieldType.Double)
    private BigDecimal ratingAverage;
    
    @Field(type = FieldType.Integer)
    private Integer totalReviews;
    
    @Field(type = FieldType.Integer)
    private Integer totalBookings;
    
    @Field(type = FieldType.Boolean)
    private Boolean isVerified;
    
    @Field(type = FieldType.Boolean)
    private Boolean isActive;
    
    @Field(type = FieldType.Boolean)
    private Boolean isFeatured;
    
    @Field(type = FieldType.Double)
    private BigDecimal startingPrice;
    
    @Field(type = FieldType.Double)
    private BigDecimal maxPrice;
    
    @Field(type = FieldType.Integer)
    private Integer yearsOfExperience;
    
    @Field(type = FieldType.Text, analyzer = "standard")
    private List<String> services;
    
    @Field(type = FieldType.Keyword)
    private List<String> serviceAreas;
    
    @Field(type = FieldType.Text, analyzer = "standard")
    private List<String> features;
    
    @Field(type = FieldType.Keyword)
    private List<String> paymentMethods;
    
    @Field(type = FieldType.Keyword)
    private List<String> languages;
    
    @Field(type = FieldType.Text)
    private String profileImageUrl;
    
    @Field(type = FieldType.Text)
    private List<String> galleryImages;
    
    @Field(type = FieldType.Date)
    private LocalDateTime createdAt;
    
    @Field(type = FieldType.Date)
    private LocalDateTime updatedAt;
    
    // Completion suggester for autocomplete
    @CompletionField(maxInputLength = 100)
    private Suggest suggest;
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Suggest {
        private String[] input;
        private Integer weight;
    }
}
