package com.weddingbazaar.booking;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.netflix.eureka.EnableEurekaClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import org.springframework.kafka.annotation.EnableKafka;

/**
 * Booking Service Application
 */
@SpringBootApplication(scanBasePackages = {"com.weddingbazaar.booking", "com.weddingbazaar.common"})
@EnableEurekaClient
@EnableFeignClients
@EnableJpaAuditing
@EnableKafka
public class BookingServiceApplication {
    
    public static void main(String[] args) {
        SpringApplication.run(BookingServiceApplication.class, args);
    }
}
