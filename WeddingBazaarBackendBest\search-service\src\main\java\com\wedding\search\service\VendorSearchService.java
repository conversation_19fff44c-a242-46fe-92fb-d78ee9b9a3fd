package com.wedding.search.service;

import com.wedding.search.dto.SearchRequest;
import com.wedding.search.dto.SearchResponse;
import com.wedding.search.dto.SuggestionResponse;
import com.wedding.search.model.VendorSearchDocument;
import com.wedding.shared.dto.ApiResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.elasticsearch.client.elc.ElasticsearchTemplate;
import org.springframework.data.elasticsearch.core.SearchHit;
import org.springframework.data.elasticsearch.core.SearchHits;
import org.springframework.data.elasticsearch.core.query.NativeSearchQuery;
import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;
import org.springframework.data.elasticsearch.core.suggest.Completion;
import org.springframework.data.elasticsearch.core.suggest.CompletionSuggestion;
import org.springframework.data.elasticsearch.core.suggest.Suggest;
import org.springframework.data.elasticsearch.core.suggest.SuggestBuilder;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Enhanced vendor search service with comprehensive search capabilities
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class VendorSearchService {
    
    private final ElasticsearchTemplate elasticsearchTemplate;
    
    public ApiResponse<SearchResponse> searchVendors(SearchRequest request) {
        log.info("Searching vendors with criteria: {}", request);
        
        try {
            NativeSearchQuery searchQuery = buildSearchQuery(request);
            SearchHits<VendorSearchDocument> searchHits = elasticsearchTemplate.search(searchQuery, VendorSearchDocument.class);
            
            List<SearchResponse.VendorResult> vendors = searchHits.getSearchHits().stream()
                    .map(this::mapToVendorResult)
                    .collect(Collectors.toList());
            
            // Extract aggregations for faceted search
            Map<String, List<SearchResponse.FacetItem>> facets = extractFacets(searchHits);
            
            SearchResponse response = SearchResponse.builder()
                    .vendors(vendors)
                    .totalResults(searchHits.getTotalHits())
                    .page(request.getPage())
                    .size(request.getSize())
                    .facets(facets)
                    .searchTime(searchHits.getSearchTime())
                    .build();
            
            return ApiResponse.success(response);
            
        } catch (Exception e) {
            log.error("Error searching vendors", e);
            return ApiResponse.error("Search failed: " + e.getMessage());
        }
    }
    
    public ApiResponse<List<SuggestionResponse>> getAutocompleteSuggestions(String query, String category, String city, int size) {
        log.info("Getting autocomplete suggestions for query: {}", query);
        
        try {
            SuggestBuilder suggestBuilder = new SuggestBuilder();
            
            Completion.Builder completionBuilder = Completion.builder()
                    .field("suggest")
                    .prefix(query)
                    .size(size);
            
            // Add context filters
            Map<String, List<String>> contexts = new HashMap<>();
            if (category != null) {
                contexts.put("category", List.of(category));
            }
            if (city != null) {
                contexts.put("city", List.of(city));
            }
            
            if (!contexts.isEmpty()) {
                completionBuilder.contexts(contexts);
            }
            
            suggestBuilder.addSuggestion("vendor-suggest", completionBuilder.build());
            
            Suggest suggest = elasticsearchTemplate.suggest(suggestBuilder.build(), VendorSearchDocument.class);
            
            List<SuggestionResponse> suggestions = suggest.getSuggestion("vendor-suggest")
                    .getEntries()
                    .stream()
                    .flatMap(entry -> ((CompletionSuggestion.Entry) entry).getOptions().stream())
                    .map(option -> SuggestionResponse.builder()
                            .text(option.getText().string())
                            .score(option.getScore())
                            .build())
                    .collect(Collectors.toList());
            
            return ApiResponse.success(suggestions);
            
        } catch (Exception e) {
            log.error("Error getting autocomplete suggestions", e);
            return ApiResponse.error("Autocomplete failed: " + e.getMessage());
        }
    }
    
    public ApiResponse<SearchResponse> searchNearbyVendors(double latitude, double longitude, 
                                                         double radiusKm, SearchRequest request) {
        log.info("Searching nearby vendors at lat: {}, lon: {}, radius: {} km", 
                latitude, longitude, radiusKm);
        
        try {
            NativeSearchQueryBuilder queryBuilder = new NativeSearchQueryBuilder();
            
            // Build base query
            BoolQueryBuilder boolQuery = buildBaseQuery(request);
            
            // Add geo-distance filter
            boolQuery.filter(QueryBuilders.geoDistanceQuery("location")
                    .point(latitude, longitude)
                    .distance(radiusKm + "km"));
            
            queryBuilder.withQuery(boolQuery);
            
            // Add geo-distance sorting
            queryBuilder.withSort(SortBuilders.geoDistanceSort("location", latitude, longitude)
                    .order(SortOrder.ASC)
                    .unit(org.elasticsearch.common.unit.DistanceUnit.KILOMETERS));
            
            // Add other sorting
            addSorting(queryBuilder, request);
            
            // Add pagination
            Pageable pageable = PageRequest.of(request.getPage(), request.getSize());
            queryBuilder.withPageable(pageable);
            
            // Add aggregations
            addAggregations(queryBuilder);
            
            NativeSearchQuery searchQuery = queryBuilder.build();
            SearchHits<VendorSearchDocument> searchHits = elasticsearchTemplate.search(searchQuery, VendorSearchDocument.class);
            
            List<SearchResponse.VendorResult> vendors = searchHits.getSearchHits().stream()
                    .map(hit -> {
                        SearchResponse.VendorResult vendor = mapToVendorResult(hit);
                        // Add distance information
                        if (hit.getSortValues() != null && hit.getSortValues().length > 0) {
                            vendor.setDistanceKm((Double) hit.getSortValues()[0]);
                        }
                        return vendor;
                    })
                    .collect(Collectors.toList());
            
            Map<String, List<SearchResponse.FacetItem>> facets = extractFacets(searchHits);
            
            SearchResponse response = SearchResponse.builder()
                    .vendors(vendors)
                    .totalResults(searchHits.getTotalHits())
                    .page(request.getPage())
                    .size(request.getSize())
                    .facets(facets)
                    .searchTime(searchHits.getSearchTime())
                    .build();
            
            return ApiResponse.success(response);
            
        } catch (Exception e) {
            log.error("Error searching nearby vendors", e);
            return ApiResponse.error("Nearby search failed: " + e.getMessage());
        }
    }
    
    private NativeSearchQuery buildSearchQuery(SearchRequest request) {
        NativeSearchQueryBuilder queryBuilder = new NativeSearchQueryBuilder();
        
        // Build query
        BoolQueryBuilder boolQuery = buildBaseQuery(request);
        queryBuilder.withQuery(boolQuery);
        
        // Add sorting
        addSorting(queryBuilder, request);
        
        // Add pagination
        Pageable pageable = PageRequest.of(request.getPage(), request.getSize());
        queryBuilder.withPageable(pageable);
        
        // Add aggregations for faceted search
        addAggregations(queryBuilder);
        
        return queryBuilder.build();
    }
    
    private BoolQueryBuilder buildBaseQuery(SearchRequest request) {
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        
        // Text search
        if (request.getQuery() != null && !request.getQuery().trim().isEmpty()) {
            boolQuery.must(QueryBuilders.multiMatchQuery(request.getQuery())
                    .field("businessName", 3.0f)
                    .field("businessName.autocomplete", 2.0f)
                    .field("description", 1.0f)
                    .field("shortDescription", 1.5f)
                    .field("specializations", 2.0f)
                    .field("services.name", 2.0f)
                    .field("services.features", 1.0f)
                    .type(org.elasticsearch.index.query.MultiMatchQueryBuilder.Type.BEST_FIELDS)
                    .fuzziness("AUTO"));
        }
        
        // Category filter
        if (request.getCategory() != null) {
            boolQuery.filter(QueryBuilders.termQuery("category", request.getCategory()));
        }
        
        // Subcategory filter
        if (request.getSubcategories() != null && !request.getSubcategories().isEmpty()) {
            boolQuery.filter(QueryBuilders.termsQuery("subcategories", request.getSubcategories()));
        }
        
        // Location filters
        if (request.getCity() != null) {
            boolQuery.filter(QueryBuilders.termQuery("city", request.getCity()));
        }
        
        if (request.getState() != null) {
            boolQuery.filter(QueryBuilders.termQuery("state", request.getState()));
        }
        
        // Price range filter
        if (request.getMinPrice() != null || request.getMaxPrice() != null) {
            var rangeQuery = QueryBuilders.rangeQuery("startingPrice");
            if (request.getMinPrice() != null) {
                rangeQuery.gte(request.getMinPrice());
            }
            if (request.getMaxPrice() != null) {
                rangeQuery.lte(request.getMaxPrice());
            }
            boolQuery.filter(rangeQuery);
        }
        
        // Rating filter
        if (request.getMinRating() != null) {
            boolQuery.filter(QueryBuilders.rangeQuery("ratingAverage").gte(request.getMinRating()));
        }
        
        // Experience filter
        if (request.getMinExperience() != null) {
            boolQuery.filter(QueryBuilders.rangeQuery("yearsOfExperience").gte(request.getMinExperience()));
        }
        
        // Verification filter
        if (request.getVerifiedOnly() != null && request.getVerifiedOnly()) {
            boolQuery.filter(QueryBuilders.termQuery("isVerified", true));
        }
        
        // Premium filter
        if (request.getPremiumOnly() != null && request.getPremiumOnly()) {
            boolQuery.filter(QueryBuilders.termQuery("isPremium", true));
        }
        
        // Languages filter
        if (request.getLanguages() != null && !request.getLanguages().isEmpty()) {
            boolQuery.filter(QueryBuilders.termsQuery("languages", request.getLanguages()));
        }
        
        // Service areas filter
        if (request.getServiceAreas() != null && !request.getServiceAreas().isEmpty()) {
            boolQuery.filter(QueryBuilders.termsQuery("serviceAreas.keyword", request.getServiceAreas()));
        }
        
        // Active vendors only
        boolQuery.filter(QueryBuilders.termQuery("isActive", true));
        
        return boolQuery;
    }
    
    private void addSorting(NativeSearchQueryBuilder queryBuilder, SearchRequest request) {
        if (request.getSortBy() != null) {
            switch (request.getSortBy().toLowerCase()) {
                case "rating":
                    queryBuilder.withSort(SortBuilders.fieldSort("ratingAverage").order(SortOrder.DESC));
                    break;
                case "price_low":
                    queryBuilder.withSort(SortBuilders.fieldSort("startingPrice").order(SortOrder.ASC));
                    break;
                case "price_high":
                    queryBuilder.withSort(SortBuilders.fieldSort("startingPrice").order(SortOrder.DESC));
                    break;
                case "experience":
                    queryBuilder.withSort(SortBuilders.fieldSort("yearsOfExperience").order(SortOrder.DESC));
                    break;
                case "popularity":
                    queryBuilder.withSort(SortBuilders.fieldSort("totalBookings").order(SortOrder.DESC));
                    break;
                case "newest":
                    queryBuilder.withSort(SortBuilders.fieldSort("createdAt").order(SortOrder.DESC));
                    break;
                default:
                    // Default sorting: featured first, then by rating, then by relevance
                    queryBuilder.withSort(SortBuilders.fieldSort("isFeatured").order(SortOrder.DESC));
                    queryBuilder.withSort(SortBuilders.fieldSort("ratingAverage").order(SortOrder.DESC));
                    queryBuilder.withSort(SortBuilders.scoreSort().order(SortOrder.DESC));
            }
        } else {
            // Default sorting
            queryBuilder.withSort(SortBuilders.fieldSort("isFeatured").order(SortOrder.DESC));
            queryBuilder.withSort(SortBuilders.fieldSort("ratingAverage").order(SortOrder.DESC));
            queryBuilder.withSort(SortBuilders.scoreSort().order(SortOrder.DESC));
        }
    }
    
    private void addAggregations(NativeSearchQueryBuilder queryBuilder) {
        queryBuilder.addAggregation(AggregationBuilders.terms("categories").field("category").size(20));
        queryBuilder.addAggregation(AggregationBuilders.terms("cities").field("city").size(50));
        queryBuilder.addAggregation(AggregationBuilders.terms("states").field("state").size(30));
        queryBuilder.addAggregation(AggregationBuilders.range("price_ranges").field("startingPrice")
                .addRange("budget", 0, 25000)
                .addRange("mid_range", 25000, 75000)
                .addRange("premium", 75000, 200000)
                .addRange("luxury", 200000, Double.MAX_VALUE));
        queryBuilder.addAggregation(AggregationBuilders.range("rating_ranges").field("ratingAverage")
                .addRange("4_plus", 4.0, 5.0)
                .addRange("3_plus", 3.0, 4.0)
                .addRange("2_plus", 2.0, 3.0));
        queryBuilder.addAggregation(AggregationBuilders.terms("languages").field("languages").size(10));
    }
    
    private SearchResponse.VendorResult mapToVendorResult(SearchHit<VendorSearchDocument> hit) {
        VendorSearchDocument doc = hit.getContent();
        
        return SearchResponse.VendorResult.builder()
                .vendorId(doc.getVendorId())
                .businessName(doc.getBusinessName())
                .shortDescription(doc.getShortDescription())
                .category(doc.getCategory())
                .subcategories(doc.getSubcategories())
                .city(doc.getCity())
                .state(doc.getState())
                .ratingAverage(doc.getRatingAverage())
                .totalReviews(doc.getTotalReviews())
                .isVerified(doc.getIsVerified())
                .isFeatured(doc.getIsFeatured())
                .isPremium(doc.getIsPremium())
                .startingPrice(doc.getStartingPrice())
                .maxPrice(doc.getMaxPrice())
                .yearsOfExperience(doc.getYearsOfExperience())
                .profileImageUrl(doc.getProfileImageUrl())
                .galleryImages(doc.getGalleryImages())
                .specializations(doc.getSpecializations())
                .languages(doc.getLanguages())
                .responseTimeHours(doc.getResponseTimeHours())
                .services(doc.getServices())
                .score(hit.getScore())
                .build();
    }
    
    private Map<String, List<SearchResponse.FacetItem>> extractFacets(SearchHits<VendorSearchDocument> searchHits) {
        Map<String, List<SearchResponse.FacetItem>> facets = new HashMap<>();
        
        if (searchHits.getAggregations() != null) {
            searchHits.getAggregations().forEach(aggregation -> {
                if (aggregation instanceof Terms) {
                    Terms terms = (Terms) aggregation;
                    List<SearchResponse.FacetItem> items = terms.getBuckets().stream()
                            .map(bucket -> SearchResponse.FacetItem.builder()
                                    .key(bucket.getKeyAsString())
                                    .count(bucket.getDocCount())
                                    .build())
                            .collect(Collectors.toList());
                    facets.put(aggregation.getName(), items);
                }
            });
        }
        
        return facets;
    }
}
