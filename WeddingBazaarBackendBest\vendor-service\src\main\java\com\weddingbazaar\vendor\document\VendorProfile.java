package com.weddingbazaar.vendor.document;

import com.weddingbazaar.common.enums.VendorCategory;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.Id;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.mongodb.core.mapping.Document;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * Vendor profile document in MongoDB for flexible data
 */
@Document(collection = "vendor_profiles")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class VendorProfile {
    
    @Id
    private String id;
    
    private String vendorId; // Reference to Vendor entity
    
    private VendorCategory category;
    
    // Dynamic fields based on vendor category
    private Map<String, Object> categorySpecificData;
    
    // Portfolio and work samples
    private List<Portfolio> portfolios;
    
    // Awards and certifications
    private List<Award> awards;
    
    // Team information
    private List<TeamMember> teamMembers;
    
    // Equipment and facilities
    private List<Equipment> equipment;
    
    // Packages offered
    private List<Package> packages;
    
    // Availability calendar
    private Map<String, Object> availability;
    
    // Reviews and testimonials
    private List<Testimonial> testimonials;
    
    // Social media metrics
    private SocialMediaMetrics socialMetrics;
    
    // Business hours
    private BusinessHours businessHours;
    
    // Payment methods accepted
    private List<String> paymentMethods;
    
    // Languages spoken
    private List<String> languages;
    
    // Service areas
    private List<String> serviceAreas;
    
    @CreatedDate
    private LocalDateTime createdAt;
    
    @LastModifiedDate
    private LocalDateTime updatedAt;
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Portfolio {
        private String title;
        private String description;
        private List<String> imageUrls;
        private String videoUrl;
        private String eventType;
        private LocalDateTime eventDate;
        private String location;
    }
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Award {
        private String title;
        private String issuedBy;
        private LocalDateTime issuedDate;
        private String description;
        private String certificateUrl;
    }
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TeamMember {
        private String name;
        private String role;
        private String bio;
        private String imageUrl;
        private Integer yearsOfExperience;
        private List<String> specializations;
    }
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Equipment {
        private String name;
        private String type;
        private String brand;
        private String model;
        private String description;
        private Boolean isAvailable;
    }
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Package {
        private String name;
        private String description;
        private Double price;
        private String duration;
        private List<String> inclusions;
        private List<String> exclusions;
        private Boolean isPopular;
        private Boolean isCustomizable;
    }
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Testimonial {
        private String clientName;
        private String content;
        private Integer rating;
        private LocalDateTime eventDate;
        private String eventType;
        private List<String> imageUrls;
    }
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SocialMediaMetrics {
        private Integer facebookFollowers;
        private Integer instagramFollowers;
        private Integer youtubeSubscribers;
        private Double engagementRate;
        private LocalDateTime lastUpdated;
    }
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class BusinessHours {
        private Map<String, DaySchedule> weeklySchedule;
        private List<String> holidays;
        private String timezone;
    }
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DaySchedule {
        private Boolean isOpen;
        private String openTime;
        private String closeTime;
        private String breakStartTime;
        private String breakEndTime;
    }
}
