server:
  port: 8082

spring:
  application:
    name: vendor-service
  datasource:
    url: ************************************************
    username: ${DB_USERNAME:postgres}
    password: ${DB_PASSWORD:postgres}
  data:
    mongodb:
      host: ${mongodb.host}
      port: ${mongodb.port}
      database: wedding_vendor_profiles
      username: ${mongodb.username}
      password: ${mongodb.password}
  jpa:
    hibernate:
      ddl-auto: validate
  flyway:
    locations: classpath:db/migration
    baseline-version: 1

# Vendor Service Specific Configuration
vendor-service:
  verification:
    auto-approve: false
    required-documents:
      - business_license
      - tax_certificate
      - identity_proof
      - address_proof
    verification-expiry-days: 365
  subscription:
    free-tier:
      max-services: 3
      max-images: 10
      featured-listings: 0
    basic-tier:
      max-services: 10
      max-images: 50
      featured-listings: 1
      price-monthly: 999
    premium-tier:
      max-services: 50
      max-images: 200
      featured-listings: 5
      price-monthly: 2999
    enterprise-tier:
      max-services: -1 # unlimited
      max-images: -1 # unlimited
      featured-listings: 10
      price-monthly: 9999
  commission:
    default-rate: 10.0
    premium-rate: 8.0
    enterprise-rate: 5.0
  search:
    sync-to-elasticsearch: true
    sync-delay-seconds: 30

logging:
  level:
    com.wedding.vendor: DEBUG
    org.springframework.data.mongodb: DEBUG
