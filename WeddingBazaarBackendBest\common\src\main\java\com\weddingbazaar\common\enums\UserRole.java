package com.weddingbazaar.common.enums;

/**
 * User roles in the system
 */
public enum UserRole {
    CUSTOMER("Customer"),
    VENDOR("Vendor"),
    ADMIN("Admin"),
    SUPER_ADMIN("Super Admin");
    
    private final String displayName;
    
    UserRole(String displayName) {
        this.displayName = displayName;
    }
    
    public String getDisplayName() {
        return displayName;
    }
    
    public boolean isAdmin() {
        return this == ADMIN || this == SUPER_ADMIN;
    }
    
    public boolean isVendor() {
        return this == VENDOR;
    }
    
    public boolean isCustomer() {
        return this == CUSTOMER;
    }
}
