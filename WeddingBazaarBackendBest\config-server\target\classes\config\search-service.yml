server:
  port: 8083

spring:
  application:
    name: search-service
  elasticsearch:
    uris: http://${elasticsearch.host}:${elasticsearch.port}
    username: ${elasticsearch.username}
    password: ${elasticsearch.password}
    connection-timeout: 10s
    socket-timeout: 30s

# Search Service Specific Configuration
search-service:
  elasticsearch:
    indices:
      vendors:
        name: vendors
        shards: 3
        replicas: 1
        refresh-interval: 1s
      services:
        name: vendor_services
        shards: 2
        replicas: 1
        refresh-interval: 5s
  search:
    default-size: 20
    max-size: 100
    highlight-enabled: true
    fuzzy-enabled: true
    autocomplete:
      max-suggestions: 10
      min-prefix-length: 2
  geo:
    default-radius-km: 50
    max-radius-km: 500
  cache:
    search-results-ttl-minutes: 15
    popular-searches-ttl-hours: 24
    autocomplete-ttl-hours: 6

logging:
  level:
    com.wedding.search: DEBUG
    org.elasticsearch: INFO
    org.springframework.data.elasticsearch: DEBUG
