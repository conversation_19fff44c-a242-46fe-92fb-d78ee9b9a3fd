spring:
  application:
    name: wedding-planner-backend
  
  profiles:
    active: ${SPRING_PROFILES_ACTIVE:dev}
  
  datasource:
    url: jdbc:mysql://${DB_HOST:localhost}:${DB_PORT:3306}/${DB_NAME:wedding_planner}?useSSL=true&serverTimezone=UTC&allowPublicKeyRetrieval=true
    username: ${DB_USERNAME:wedding_user}
    password: ${DB_PASSWORD:wedding_password}
    driver-class-name: com.mysql.cj.jdbc.Driver
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      idle-timeout: 300000
      connection-timeout: 20000
      leak-detection-threshold: 60000
      pool-name: WeddingPlannerHikariCP
  
  jpa:
    hibernate:
      ddl-auto: validate
      naming:
        physical-strategy: org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl
        implicit-strategy: org.hibernate.boot.model.naming.ImplicitNamingStrategyLegacyJpaImpl
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQL8Dialect
        format_sql: true
        use_sql_comments: true
        jdbc:
          batch_size: 20
        order_inserts: true
        order_updates: true
        generate_statistics: false
    open-in-view: false
  
  flyway:
    enabled: true
    locations: classpath:db/migration
    baseline-on-migrate: true
    validate-on-migrate: true
    clean-disabled: true
  
  redis:
    host: ${REDIS_HOST:localhost}
    port: ${REDIS_PORT:6379}
    password: ${REDIS_PASSWORD:}
    database: ${REDIS_DATABASE:0}
    timeout: 2000ms
    lettuce:
      pool:
        max-active: 8
        max-idle: 8
        min-idle: 0
        max-wait: -1ms
  
  cache:
    type: redis
    redis:
      time-to-live: 600000
      cache-null-values: false
  
  elasticsearch:
    uris: ${ELASTICSEARCH_URIS:http://localhost:9200}
    username: ${ELASTICSEARCH_USERNAME:}
    password: ${ELASTICSEARCH_PASSWORD:}
    connection-timeout: 5s
    socket-timeout: 30s
  
  mail:
    host: ${MAIL_HOST:smtp.gmail.com}
    port: ${MAIL_PORT:587}
    username: ${MAIL_USERNAME:}
    password: ${MAIL_PASSWORD:}
    properties:
      mail:
        smtp:
          auth: true
          starttls:
            enable: true
            required: true
          ssl:
            trust: ${MAIL_HOST:smtp.gmail.com}
  
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 50MB
  
  security:
    oauth2:
      client:
        registration:
          google:
            client-id: ${GOOGLE_CLIENT_ID:}
            client-secret: ${GOOGLE_CLIENT_SECRET:}
            scope: openid,profile,email
          facebook:
            client-id: ${FACEBOOK_CLIENT_ID:}
            client-secret: ${FACEBOOK_CLIENT_SECRET:}
            scope: email,public_profile

server:
  port: ${SERVER_PORT:8080}
  servlet:
    context-path: /api/v1
  compression:
    enabled: true
    mime-types: text/html,text/xml,text/plain,text/css,text/javascript,application/javascript,application/json
    min-response-size: 1024
  http2:
    enabled: true

management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
      base-path: /actuator
  endpoint:
    health:
      show-details: when-authorized
      show-components: always
  metrics:
    export:
      prometheus:
        enabled: true
  health:
    redis:
      enabled: true
    elasticsearch:
      enabled: true

logging:
  level:
    com.weddingplanner: ${LOG_LEVEL:INFO}
    org.springframework.security: ${SECURITY_LOG_LEVEL:WARN}
    org.hibernate.SQL: ${SQL_LOG_LEVEL:WARN}
    org.hibernate.type.descriptor.sql.BasicBinder: ${SQL_PARAM_LOG_LEVEL:WARN}
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/wedding-planner.log
    max-size: 10MB
    max-history: 30

# Application specific configurations
app:
  jwt:
    secret: ${JWT_SECRET:mySecretKey}
    expiration: ${JWT_EXPIRATION:86400000} # 24 hours
    refresh-expiration: ${JWT_REFRESH_EXPIRATION:604800000} # 7 days
  
  cors:
    allowed-origins: ${CORS_ALLOWED_ORIGINS:http://localhost:3000,http://localhost:3001}
    allowed-methods: GET,POST,PUT,DELETE,OPTIONS,PATCH
    allowed-headers: "*"
    allow-credentials: true
    max-age: 3600
  
  aws:
    region: ${AWS_REGION:us-east-1}
    access-key: ${AWS_ACCESS_KEY:}
    secret-key: ${AWS_SECRET_KEY:}
    s3:
      bucket-name: ${AWS_S3_BUCKET:wedding-planner-files}
      base-url: ${AWS_S3_BASE_URL:https://wedding-planner-files.s3.amazonaws.com}
  
  stripe:
    public-key: ${STRIPE_PUBLIC_KEY:}
    secret-key: ${STRIPE_SECRET_KEY:}
    webhook-secret: ${STRIPE_WEBHOOK_SECRET:}
  
  email:
    from: ${EMAIL_FROM:<EMAIL>}
    templates:
      welcome: welcome-email
      password-reset: password-reset-email
      booking-confirmation: booking-confirmation-email
  
  file:
    upload:
      max-size: 10485760 # 10MB
      allowed-types: jpg,jpeg,png,gif,pdf,doc,docx
      path: ${FILE_UPLOAD_PATH:uploads/}
  
  rate-limit:
    enabled: true
    requests-per-minute: 60
    burst-capacity: 100

# Swagger/OpenAPI Configuration
springdoc:
  api-docs:
    path: /api-docs
  swagger-ui:
    path: /swagger-ui.html
    enabled: true
    operations-sorter: method
    tags-sorter: alpha
  show-actuator: true

# Custom properties for business logic
business:
  wedding:
    planning:
      max-guests: 1000
      max-vendors: 50
      max-timeline-tasks: 100
      advance-booking-days: 365
  
  vendor:
    verification:
      required-documents: 3
      approval-timeout-days: 7
  
  payment:
    processing:
      timeout-seconds: 30
      retry-attempts: 3
  
  notification:
    email:
      batch-size: 100
      retry-attempts: 3
    sms:
      enabled: false
      provider: twilio
