package com.wedding.vendor.model;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * Enhanced Vendor entity with comprehensive business information
 */
@Entity
@Table(name = "vendors", indexes = {
    @Index(name = "idx_vendor_user_id", columnList = "user_id"),
    @Index(name = "idx_vendor_category", columnList = "category"),
    @Index(name = "idx_vendor_city_state", columnList = "city, state"),
    @Index(name = "idx_vendor_verification_status", columnList = "verification_status"),
    @Index(name = "idx_vendor_rating", columnList = "rating_average"),
    @Index(name = "idx_vendor_featured", columnList = "is_featured")
})
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EntityListeners(AuditingEntityListener.class)
public class Vendor {
    
    @Id
    @GeneratedValue(strategy = GenerationType.UUID)
    private String id;
    
    @Column(name = "user_id", nullable = false, unique = true)
    private String userId; // Reference to User service
    
    @Column(name = "business_name", nullable = false)
    private String businessName;
    
    @Column(name = "business_email")
    private String businessEmail;
    
    @Column(name = "business_phone", nullable = false)
    private String businessPhone;
    
    @Column(name = "alternate_phone")
    private String alternatePhone;
    
    @Column(name = "gst_number")
    private String gstNumber;
    
    @Column(name = "pan_number")
    private String panNumber;
    
    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private VendorCategory category;
    
    @ElementCollection
    @Enumerated(EnumType.STRING)
    @CollectionTable(name = "vendor_subcategories", joinColumns = @JoinColumn(name = "vendor_id"))
    @Column(name = "subcategory")
    private List<VendorSubcategory> subcategories;
    
    @Column(nullable = false)
    private String city;
    
    @Column(nullable = false)
    private String state;
    
    private String country = "India";
    
    @Column(name = "pincode")
    private String pincode;
    
    @Column(name = "full_address", nullable = false)
    private String fullAddress;
    
    @Column(name = "latitude")
    private Double latitude;
    
    @Column(name = "longitude")
    private Double longitude;
    
    @Column(name = "rating_average", precision = 3, scale = 2)
    private BigDecimal ratingAverage = BigDecimal.ZERO;
    
    @Column(name = "total_reviews")
    private Integer totalReviews = 0;
    
    @Column(name = "total_bookings")
    private Integer totalBookings = 0;
    
    @Column(name = "completed_bookings")
    private Integer completedBookings = 0;
    
    @Column(name = "cancelled_bookings")
    private Integer cancelledBookings = 0;
    
    @Column(name = "is_verified")
    private Boolean isVerified = false;
    
    @Column(name = "is_active")
    private Boolean isActive = true;
    
    @Column(name = "is_featured")
    private Boolean isFeatured = false;
    
    @Column(name = "is_premium")
    private Boolean isPremium = false;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "verification_status")
    private VerificationStatus verificationStatus = VerificationStatus.PENDING;
    
    @Column(name = "verification_notes")
    private String verificationNotes;
    
    @Column(name = "verified_at")
    private LocalDateTime verifiedAt;
    
    @Column(name = "verified_by")
    private String verifiedBy;
    
    @Column(name = "profile_image_url")
    private String profileImageUrl;
    
    @ElementCollection
    @CollectionTable(name = "vendor_gallery", joinColumns = @JoinColumn(name = "vendor_id"))
    @Column(name = "image_url")
    private List<String> galleryImages;
    
    @Column(name = "cover_image_url")
    private String coverImageUrl;
    
    @Column(name = "description", columnDefinition = "TEXT")
    private String description;
    
    @Column(name = "short_description")
    private String shortDescription;
    
    @Column(name = "website_url")
    private String websiteUrl;
    
    @Column(name = "facebook_url")
    private String facebookUrl;
    
    @Column(name = "instagram_url")
    private String instagramUrl;
    
    @Column(name = "youtube_url")
    private String youtubeUrl;
    
    @Column(name = "linkedin_url")
    private String linkedinUrl;
    
    @Column(name = "years_of_experience")
    private Integer yearsOfExperience;
    
    @Column(name = "team_size")
    private Integer teamSize;
    
    @Column(name = "starting_price", precision = 10, scale = 2)
    private BigDecimal startingPrice;
    
    @Column(name = "max_price", precision = 10, scale = 2)
    private BigDecimal maxPrice;
    
    @Column(name = "commission_rate", precision = 5, scale = 2)
    private BigDecimal commissionRate = new BigDecimal("10.00"); // 10% default
    
    @ElementCollection
    @CollectionTable(name = "vendor_service_areas", joinColumns = @JoinColumn(name = "vendor_id"))
    @Column(name = "service_area")
    private List<String> serviceAreas;
    
    @ElementCollection
    @CollectionTable(name = "vendor_specializations", joinColumns = @JoinColumn(name = "vendor_id"))
    @Column(name = "specialization")
    private List<String> specializations;
    
    @ElementCollection
    @CollectionTable(name = "vendor_languages", joinColumns = @JoinColumn(name = "vendor_id"))
    @Column(name = "language")
    private List<String> languages;
    
    @Column(name = "response_time_hours")
    private Integer responseTimeHours = 24; // Default 24 hours
    
    @Column(name = "advance_booking_days")
    private Integer advanceBookingDays = 30; // Default 30 days
    
    @Column(name = "cancellation_policy", columnDefinition = "TEXT")
    private String cancellationPolicy;
    
    @Column(name = "terms_and_conditions", columnDefinition = "TEXT")
    private String termsAndConditions;
    
    @Column(name = "last_active")
    private LocalDateTime lastActive;
    
    @Column(name = "subscription_plan")
    @Enumerated(EnumType.STRING)
    private SubscriptionPlan subscriptionPlan = SubscriptionPlan.FREE;
    
    @Column(name = "subscription_expires_at")
    private LocalDateTime subscriptionExpiresAt;
    
    @CreatedDate
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;
    
    @LastModifiedDate
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
    
    // Helper methods
    public Double getCompletionRate() {
        if (totalBookings == 0) return 0.0;
        return (completedBookings.doubleValue() / totalBookings.doubleValue()) * 100;
    }
    
    public Double getCancellationRate() {
        if (totalBookings == 0) return 0.0;
        return (cancelledBookings.doubleValue() / totalBookings.doubleValue()) * 100;
    }
    
    public boolean isSubscriptionActive() {
        return subscriptionExpiresAt != null && subscriptionExpiresAt.isAfter(LocalDateTime.now());
    }
    
    public boolean canAcceptBookings() {
        return isActive && isVerified && verificationStatus == VerificationStatus.VERIFIED;
    }
    
    // Enums
    public enum VerificationStatus {
        PENDING, UNDER_REVIEW, VERIFIED, REJECTED, SUSPENDED, DOCUMENTS_REQUIRED
    }
    
    public enum VendorCategory {
        PHOTOGRAPHER, VIDEOGRAPHER, VENUE, CATERING, DECORATION, MUSIC_DJ,
        MAKEUP_ARTIST, MEHENDI_ARTIST, FLORIST, WEDDING_PLANNER, TRANSPORTATION,
        INVITATION_CARDS, JEWELRY, CLOTHING, PANDIT_PRIEST, SECURITY, GIFTS,
        HONEYMOON, BRIDAL_WEAR, GROOM_WEAR, WEDDING_CAKE, LIVE_STREAMING, OTHER
    }
    
    public enum VendorSubcategory {
        // Photography subcategories
        WEDDING_PHOTOGRAPHY, PRE_WEDDING_PHOTOGRAPHY, CANDID_PHOTOGRAPHY, TRADITIONAL_PHOTOGRAPHY,
        
        // Venue subcategories
        BANQUET_HALL, RESORT, HOTEL, FARMHOUSE, DESTINATION_WEDDING, OUTDOOR_VENUE,
        
        // Catering subcategories
        NORTH_INDIAN, SOUTH_INDIAN, CHINESE, CONTINENTAL, GUJARATI, PUNJABI, BENGALI,
        
        // Music subcategories
        DJ, LIVE_BAND, CLASSICAL_MUSIC, BOLLYWOOD_MUSIC, REGIONAL_MUSIC
    }
    
    public enum SubscriptionPlan {
        FREE, BASIC, PREMIUM, ENTERPRISE
    }
}
