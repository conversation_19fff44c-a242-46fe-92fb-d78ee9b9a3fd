package com.weddingplanner.model.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * User roles in the wedding planner system
 * 
 * <AUTHOR> Planner Team
 */
@Getter
@RequiredArgsConstructor
public enum UserRole {
    CUSTOMER("Customer", "Regular customer planning a wedding"),
    VENDOR("Vendor", "Service provider offering wedding services"),
    ADMIN("Admin", "System administrator with full access"),
    SUPER_ADMIN("Super Admin", "Super administrator with system-wide privileges");

    private final String displayName;
    private final String description;

    /**
     * Get role by name (case-insensitive)
     */
    public static UserRole fromString(String role) {
        if (role == null) {
            return null;
        }
        
        for (UserRole userRole : UserRole.values()) {
            if (userRole.name().equalsIgnoreCase(role) || 
                userRole.displayName.equalsIgnoreCase(role)) {
                return userRole;
            }
        }
        
        throw new IllegalArgumentException("Invalid user role: " + role);
    }

    /**
     * Check if role has admin privileges
     */
    public boolean isAdmin() {
        return this == ADMIN || this == SUPER_ADMIN;
    }

    /**
     * Check if role has vendor privileges
     */
    public boolean isVendor() {
        return this == VENDOR;
    }

    /**
     * Check if role has customer privileges
     */
    public boolean isCustomer() {
        return this == CUSTOMER;
    }
}
