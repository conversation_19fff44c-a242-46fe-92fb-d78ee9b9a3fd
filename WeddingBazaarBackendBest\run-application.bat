@echo off
setlocal enabledelayedexpansion

REM Wedding Bazaar Backend - Windows Startup Script
REM This script starts all microservices in the correct order

echo ================================
echo   Wedding Bazaar Backend
echo   Microservices Startup
echo ================================
echo.

REM Configuration
set JAVA_OPTS=-Xmx1g -Xms512m
set SPRING_PROFILES_ACTIVE=dev
set LOG_LEVEL=INFO

REM Create logs directory
if not exist logs mkdir logs

REM Function to check if a service is running
:check_service
set service_name=%1
set port=%2
echo [INFO] Checking if %service_name% is running on port %port%...

REM Wait for service to be ready (simplified for Windows)
timeout /t 10 /nobreak >nul
curl -s -f "http://localhost:%port%/actuator/health" >nul 2>&1
if %errorlevel% equ 0 (
    echo [SUCCESS] %service_name% is running and healthy!
) else (
    echo [ERROR] %service_name% failed to start or is not healthy
)
goto :eof

REM Function to start a service
:start_service
set service_name=%1
set service_dir=%2
set port=%3

echo [STEP] Starting %service_name%...

cd %service_dir%
start /b cmd /c "mvn spring-boot:run -Dspring-boot.run.jvmArguments=\"%JAVA_OPTS%\" -Dspring.profiles.active=%SPRING_PROFILES_ACTIVE% -Dlogging.level.root=%LOG_LEVEL% > ..\logs\%service_name%.log 2>&1"

echo [INFO] %service_name% started
call :check_service "%service_name%" %port%

cd ..
goto :eof

REM Check prerequisites
echo [STEP] Checking prerequisites...

java -version >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] Java is not installed or not in PATH
    pause
    exit /b 1
)
echo [SUCCESS] Java detected

mvn -version >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] Maven is not installed or not in PATH
    pause
    exit /b 1
)
echo [SUCCESS] Maven detected

REM Build the project
echo [STEP] Building the project...
mvn clean install -DskipTests -T 4

if %errorlevel% neq 0 (
    echo [ERROR] Project build failed
    pause
    exit /b 1
)
echo [SUCCESS] Project built successfully

REM Start infrastructure message
echo [STEP] Infrastructure Setup Required
echo.
echo Please ensure the following services are running:
echo   - PostgreSQL on port 5432 (database: wedding_bazaar, user: postgres, password: postgres)
echo   - Redis on port 6379 (optional but recommended)
echo.
echo Quick setup with Docker:
echo   docker run -d --name postgres-wedding -e POSTGRES_DB=wedding_bazaar -e POSTGRES_USER=postgres -e POSTGRES_PASSWORD=postgres -p 5432:5432 postgres:15
echo   docker run -d --name redis-wedding -p 6379:6379 redis:7-alpine
echo.
pause

REM Start services in order
echo [STEP] Starting microservices in order...

call :start_service "service-registry" "service-registry" 8761
call :start_service "config-server" "config-server" 8888
call :start_service "api-gateway" "api-gateway" 8080
call :start_service "user-service" "user-service" 8081

REM Show final status
echo.
echo [SUCCESS] Wedding Bazaar Backend is now running!
echo.
echo Available endpoints:
echo   - API Gateway: http://localhost:8080
echo   - Swagger UI: http://localhost:8080/swagger-ui.html
echo   - Eureka Dashboard: http://localhost:8761
echo   - Config Server: http://localhost:8888
echo.
echo Service logs are available in the 'logs' directory
echo.
echo Press any key to stop all services...
pause >nul

REM Stop services (simplified - just close the window)
echo Stopping services...
taskkill /f /im java.exe >nul 2>&1

echo All services stopped
pause
