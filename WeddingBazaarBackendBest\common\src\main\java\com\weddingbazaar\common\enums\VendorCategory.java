package com.weddingbazaar.common.enums;

/**
 * Vendor categories in the wedding marketplace
 */
public enum VendorCategory {
    PHOTOGRAPHER("Photographer"),
    VIDEOGRAPHER("Videographer"),
    VENUE("Venue"),
    CATERING("Catering"),
    DE<PERSON>RA<PERSON><PERSON>("Decoration"),
    <PERSON><PERSON><PERSON>_<PERSON>("<PERSON> & DJ"),
    MAKE<PERSON>_ARTIST("Makeup Artist"),
    MEHENDI_ARTIST("Mehendi Artist"),
    FLORIST("Florist"),
    WEDDING_PLANNER("Wedding Planner"),
    TRANSPORTATION("Transportation"),
    INVITATION_CARDS("Invitation Cards"),
    JEWELRY("Jewelry"),
    CLOTHING("Clothing"),
    PANDIT_PRIEST("Pandit/Priest"),
    SECURITY("Security"),
    GIFTS("Gifts"),
    <PERSON><PERSON><PERSON>Y<PERSON><PERSON>("Honeymoon"),
    OTHER("Other");
    
    private final String displayName;
    
    VendorCategory(String displayName) {
        this.displayName = displayName;
    }
    
    public String getDisplayName() {
        return displayName;
    }
}
