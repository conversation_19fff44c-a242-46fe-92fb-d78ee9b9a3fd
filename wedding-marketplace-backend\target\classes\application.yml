server:
  port: 8080
  servlet:
    context-path: /api/v1
  compression:
    enabled: true
    mime-types: text/html,text/xml,text/plain,text/css,text/javascript,application/javascript,application/json
    min-response-size: 1024
  http2:
    enabled: true

spring:
  application:
    name: wedding-marketplace-backend
  
  profiles:
    active: ${SPRING_PROFILES_ACTIVE:dev}
  
  datasource:
    url: jdbc:mysql://${DB_HOST:localhost}:${DB_PORT:3306}/${DB_NAME:wedding_marketplace}?useSSL=false&serverTimezone=UTC&allowPublicKeyRetrieval=true&createDatabaseIfNotExist=true
    username: ${DB_USERNAME:root}
    password: ${DB_PASSWORD:password}
    driver-class-name: com.mysql.cj.jdbc.Driver
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      idle-timeout: 300000
      connection-timeout: 20000
      leak-detection-threshold: 60000
      pool-name: WeddingMarketplaceHikariCP
  
  jpa:
    hibernate:
      ddl-auto: validate
      naming:
        physical-strategy: org.hibernate.boot.model.naming.SnakeCasePhysicalNamingStrategy
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQL8Dialect
        format_sql: true
        use_sql_comments: true
        jdbc:
          batch_size: 20
        order_inserts: true
        order_updates: true
        generate_statistics: false
    open-in-view: false
  
  flyway:
    enabled: true
    locations: classpath:db/migration
    baseline-on-migrate: true
    validate-on-migrate: true
    clean-disabled: true
  
  redis:
    host: ${REDIS_HOST:localhost}
    port: ${REDIS_PORT:6379}
    password: ${REDIS_PASSWORD:}
    database: ${REDIS_DATABASE:0}
    timeout: 2000ms
    lettuce:
      pool:
        max-active: 8
        max-idle: 8
        min-idle: 0
        max-wait: -1ms
  
  cache:
    type: redis
    redis:
      time-to-live: 600000
      cache-null-values: false
  
  security:
    oauth2:
      client:
        registration:
          google:
            client-id: ${GOOGLE_CLIENT_ID:}
            client-secret: ${GOOGLE_CLIENT_SECRET:}
            scope: openid,profile,email
          facebook:
            client-id: ${FACEBOOK_CLIENT_ID:}
            client-secret: ${FACEBOOK_CLIENT_SECRET:}
            scope: email,public_profile
  
  mail:
    host: ${MAIL_HOST:smtp.gmail.com}
    port: ${MAIL_PORT:587}
    username: ${MAIL_USERNAME:}
    password: ${MAIL_PASSWORD:}
    properties:
      mail:
        smtp:
          auth: true
          starttls:
            enable: true
            required: true
          ssl:
            trust: "*"
  
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 10MB
      enabled: true
  
  jackson:
    serialization:
      write-dates-as-timestamps: false
    time-zone: UTC
    default-property-inclusion: NON_NULL

# Application Configuration
app:
  jwt:
    secret: ${JWT_SECRET:mySecretKey}
    expiration: ${JWT_EXPIRATION:86400000} # 24 hours
    refresh-expiration: ${JWT_REFRESH_EXPIRATION:604800000} # 7 days
  
  cors:
    allowed-origins: ${CORS_ALLOWED_ORIGINS:http://localhost:3000,http://localhost:3001}
    allowed-methods: GET,POST,PUT,DELETE,OPTIONS,PATCH
    allowed-headers: "*"
    allow-credentials: true
    max-age: 3600
  
  rate-limit:
    enabled: true
    requests-per-minute: 60
    burst-capacity: 100
  
  file-upload:
    max-size: 10MB
    allowed-types: image/jpeg,image/png,image/gif,image/webp,video/mp4,video/avi,application/pdf
    upload-dir: ${UPLOAD_DIR:/tmp/uploads}
  
  pagination:
    default-page-size: 20
    max-page-size: 100

# AWS Configuration
aws:
  region: ${AWS_REGION:us-east-1}
  access-key: ${AWS_ACCESS_KEY:}
  secret-key: ${AWS_SECRET_KEY:}
  s3:
    bucket-name: ${AWS_S3_BUCKET:wedding-marketplace-uploads}
    base-url: ${AWS_S3_BASE_URL:https://wedding-marketplace-uploads.s3.amazonaws.com}
  ses:
    from-email: ${AWS_SES_FROM_EMAIL:<EMAIL>}
    from-name: ${AWS_SES_FROM_NAME:Wedding Marketplace}

# Payment Gateway Configuration
payment:
  stripe:
    public-key: ${STRIPE_PUBLIC_KEY:}
    secret-key: ${STRIPE_SECRET_KEY:}
    webhook-secret: ${STRIPE_WEBHOOK_SECRET:}
    currency: ${STRIPE_CURRENCY:usd}
  razorpay:
    key-id: ${RAZORPAY_KEY_ID:}
    key-secret: ${RAZORPAY_KEY_SECRET:}
    webhook-secret: ${RAZORPAY_WEBHOOK_SECRET:}
    currency: ${RAZORPAY_CURRENCY:INR}

# Notification Configuration
notification:
  twilio:
    account-sid: ${TWILIO_ACCOUNT_SID:}
    auth-token: ${TWILIO_AUTH_TOKEN:}
    phone-number: ${TWILIO_PHONE_NUMBER:}
  email:
    provider: ${EMAIL_PROVIDER:aws-ses} # aws-ses, smtp
    templates:
      welcome: welcome-email
      verification: email-verification
      password-reset: password-reset
      booking-confirmation: booking-confirmation
  websocket:
    enabled: true
    endpoint: /ws
    topic-prefix: /topic
    user-prefix: /user

# Monitoring & Observability
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus,env
      base-path: /actuator
  endpoint:
    health:
      show-details: when-authorized
      show-components: always
    metrics:
      enabled: true
    prometheus:
      enabled: true
  metrics:
    export:
      prometheus:
        enabled: true
    distribution:
      percentiles-histogram:
        http.server.requests: true
      percentiles:
        http.server.requests: 0.5,0.9,0.95,0.99
  health:
    redis:
      enabled: true
    db:
      enabled: true

# Logging Configuration
logging:
  level:
    com.weddingmarketplace: INFO
    org.springframework.security: DEBUG
    org.springframework.web: INFO
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/wedding-marketplace.log
    max-size: 10MB
    max-history: 30

# Sentry Configuration
sentry:
  dsn: ${SENTRY_DSN:}
  environment: ${SPRING_PROFILES_ACTIVE:dev}
  traces-sample-rate: 1.0

# OpenAPI Documentation
springdoc:
  api-docs:
    path: /api-docs
  swagger-ui:
    path: /swagger-ui.html
    enabled: true
    operations-sorter: method
    tags-sorter: alpha
  show-actuator: true
  group-configs:
    - group: auth
      paths-to-match: /auth/**
    - group: users
      paths-to-match: /users/**
    - group: vendors
      paths-to-match: /vendors/**
    - group: bookings
      paths-to-match: /bookings/**
    - group: admin
      paths-to-match: /admin/**

---
# Development Profile
spring:
  config:
    activate:
      on-profile: dev
  
  jpa:
    show-sql: true
    properties:
      hibernate:
        generate_statistics: true
  
  logging:
    level:
      com.weddingmarketplace: DEBUG
      org.springframework.security: DEBUG

---
# Test Profile
spring:
  config:
    activate:
      on-profile: test
  
  datasource:
    url: jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
    driver-class-name: org.h2.Driver
    username: sa
    password: password
  
  jpa:
    hibernate:
      ddl-auto: create-drop
    database-platform: org.hibernate.dialect.H2Dialect
  
  flyway:
    enabled: false
  
  redis:
    host: localhost
    port: 6370

---
# Production Profile
spring:
  config:
    activate:
      on-profile: prod
  
  jpa:
    show-sql: false
    properties:
      hibernate:
        generate_statistics: false
  
  logging:
    level:
      com.weddingmarketplace: WARN
      org.springframework.security: WARN
      org.hibernate.SQL: WARN
