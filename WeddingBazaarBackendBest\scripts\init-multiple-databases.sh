#!/bin/bash

# Script to initialize multiple PostgreSQL databases for Wedding Bazaar Backend
# This script is used by Docker PostgreSQL container

set -e

# Function to create database if it doesn't exist
create_database() {
    local database=$1
    echo "Creating database: $database"
    
    psql -v ON_ERROR_STOP=1 --username "$POSTGRES_USER" --dbname "$POSTGRES_DB" <<-EOSQL
        SELECT 'CREATE DATABASE $database'
        WHERE NOT EXISTS (SELECT FROM pg_database WHERE datname = '$database')\gexec
EOSQL
    
    echo "Database $database created successfully"
}

# Function to create user and grant permissions
create_user_and_permissions() {
    local database=$1
    local username=$2
    local password=$3
    
    echo "Creating user $username for database $database"
    
    psql -v ON_ERROR_STOP=1 --username "$POSTGRES_USER" --dbname "$database" <<-EOSQL
        DO \$\$
        BEGIN
            IF NOT EXISTS (SELECT FROM pg_catalog.pg_user WHERE usename = '$username') THEN
                CREATE USER $username WITH PASSWORD '$password';
            END IF;
        END
        \$\$;
        
        GRANT ALL PRIVILEGES ON DATABASE $database TO $username;
        GRANT ALL ON SCHEMA public TO $username;
        GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO $username;
        GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO $username;
        ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO $username;
        ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO $username;
EOSQL
    
    echo "User $username created and permissions granted for database $database"
}

# Main execution
echo "Starting database initialization..."

# Create databases for each microservice
create_database "wedding_users"
create_database "wedding_vendors"
create_database "wedding_bookings"
create_database "wedding_payments"
create_database "wedding_analytics"
create_database "wedding_notifications"

# Create service-specific users (optional, for better security)
create_user_and_permissions "wedding_users" "user_service" "user_service_password"
create_user_and_permissions "wedding_vendors" "vendor_service" "vendor_service_password"
create_user_and_permissions "wedding_bookings" "booking_service" "booking_service_password"
create_user_and_permissions "wedding_payments" "payment_service" "payment_service_password"
create_user_and_permissions "wedding_analytics" "analytics_service" "analytics_service_password"
create_user_and_permissions "wedding_notifications" "notification_service" "notification_service_password"

echo "Database initialization completed successfully!"
