package com.wedding.user.event;

import com.wedding.user.model.User;
import com.wedding.user.audit.UserSnapshot;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.support.SendResult;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionPhase;
import org.springframework.transaction.event.TransactionalEventListener;

import java.time.LocalDateTime;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;

/**
 * Advanced event publisher with event sourcing and distributed event handling
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class UserEventPublisher {
    
    private final ApplicationEventPublisher applicationEventPublisher;
    private final KafkaTemplate<String, Object> kafkaTemplate;
    private final UserEventStore eventStore;
    
    // Topic names
    private static final String USER_EVENTS_TOPIC = "user-events";
    private static final String USER_NOTIFICATIONS_TOPIC = "user-notifications";
    private static final String USER_ANALYTICS_TOPIC = "user-analytics";
    
    /**
     * Publishes user registered event
     */
    public void publishUserRegisteredEvent(User user) {
        UserRegisteredEvent event = UserRegisteredEvent.builder()
                .eventId(UUID.randomUUID().toString())
                .userId(user.getId())
                .email(user.getEmail())
                .firstName(user.getFirstName())
                .lastName(user.getLastName())
                .registrationSource("WEB")
                .timestamp(LocalDateTime.now())
                .build();
        
        // Store event for event sourcing
        eventStore.saveEvent(event);
        
        // Publish local event
        applicationEventPublisher.publishEvent(event);
        
        // Publish to Kafka for distributed processing
        publishToKafka(USER_EVENTS_TOPIC, user.getId(), event);
        
        log.info("Published UserRegisteredEvent for user: {}", user.getId());
    }
    
    /**
     * Publishes user updated event with before/after snapshots
     */
    public void publishUserUpdatedEvent(User user, UserSnapshot before, UserSnapshot after) {
        UserUpdatedEvent event = UserUpdatedEvent.builder()
                .eventId(UUID.randomUUID().toString())
                .userId(user.getId())
                .beforeSnapshot(before)
                .afterSnapshot(after)
                .changedFields(calculateChangedFields(before, after))
                .timestamp(LocalDateTime.now())
                .build();
        
        eventStore.saveEvent(event);
        applicationEventPublisher.publishEvent(event);
        publishToKafka(USER_EVENTS_TOPIC, user.getId(), event);
        
        log.info("Published UserUpdatedEvent for user: {}", user.getId());
    }
    
    /**
     * Publishes email verified event
     */
    public void publishEmailVerifiedEvent(User user) {
        EmailVerifiedEvent event = EmailVerifiedEvent.builder()
                .eventId(UUID.randomUUID().toString())
                .userId(user.getId())
                .email(user.getEmail())
                .verifiedAt(LocalDateTime.now())
                .timestamp(LocalDateTime.now())
                .build();
        
        eventStore.saveEvent(event);
        applicationEventPublisher.publishEvent(event);
        publishToKafka(USER_EVENTS_TOPIC, user.getId(), event);
        
        log.info("Published EmailVerifiedEvent for user: {}", user.getId());
    }
    
    /**
     * Publishes password changed event
     */
    public void publishPasswordChangedEvent(User user) {
        PasswordChangedEvent event = PasswordChangedEvent.builder()
                .eventId(UUID.randomUUID().toString())
                .userId(user.getId())
                .email(user.getEmail())
                .changedAt(LocalDateTime.now())
                .timestamp(LocalDateTime.now())
                .build();
        
        eventStore.saveEvent(event);
        applicationEventPublisher.publishEvent(event);
        publishToKafka(USER_EVENTS_TOPIC, user.getId(), event);
        
        log.info("Published PasswordChangedEvent for user: {}", user.getId());
    }
    
    /**
     * Publishes user locked event
     */
    public void publishUserLockedEvent(User user, String reason) {
        UserLockedEvent event = UserLockedEvent.builder()
                .eventId(UUID.randomUUID().toString())
                .userId(user.getId())
                .email(user.getEmail())
                .reason(reason)
                .lockedAt(LocalDateTime.now())
                .timestamp(LocalDateTime.now())
                .build();
        
        eventStore.saveEvent(event);
        applicationEventPublisher.publishEvent(event);
        publishToKafka(USER_EVENTS_TOPIC, user.getId(), event);
        
        log.info("Published UserLockedEvent for user: {} with reason: {}", user.getId(), reason);
    }
    
    /**
     * Publishes user login event
     */
    public void publishUserLoginEvent(User user, String ipAddress, String userAgent) {
        UserLoginEvent event = UserLoginEvent.builder()
                .eventId(UUID.randomUUID().toString())
                .userId(user.getId())
                .email(user.getEmail())
                .ipAddress(ipAddress)
                .userAgent(userAgent)
                .loginAt(LocalDateTime.now())
                .timestamp(LocalDateTime.now())
                .build();
        
        eventStore.saveEvent(event);
        applicationEventPublisher.publishEvent(event);
        publishToKafka(USER_ANALYTICS_TOPIC, user.getId(), event);
        
        log.debug("Published UserLoginEvent for user: {}", user.getId());
    }
    
    /**
     * Publishes user logout event
     */
    public void publishUserLogoutEvent(User user, String sessionId) {
        UserLogoutEvent event = UserLogoutEvent.builder()
                .eventId(UUID.randomUUID().toString())
                .userId(user.getId())
                .sessionId(sessionId)
                .logoutAt(LocalDateTime.now())
                .timestamp(LocalDateTime.now())
                .build();
        
        eventStore.saveEvent(event);
        applicationEventPublisher.publishEvent(event);
        publishToKafka(USER_ANALYTICS_TOPIC, user.getId(), event);
        
        log.debug("Published UserLogoutEvent for user: {}", user.getId());
    }
    
    /**
     * Generic method to publish events to Kafka
     */
    private void publishToKafka(String topic, String key, Object event) {
        try {
            CompletableFuture<SendResult<String, Object>> future = kafkaTemplate.send(topic, key, event);
            
            future.whenComplete((result, ex) -> {
                if (ex == null) {
                    log.debug("Event published to Kafka successfully: topic={}, key={}, offset={}", 
                            topic, key, result.getRecordMetadata().offset());
                } else {
                    log.error("Failed to publish event to Kafka: topic={}, key={}", topic, key, ex);
                }
            });
        } catch (Exception e) {
            log.error("Error publishing event to Kafka: topic={}, key={}", topic, key, e);
        }
    }
    
    /**
     * Calculates changed fields between before and after snapshots
     */
    private java.util.List<String> calculateChangedFields(UserSnapshot before, UserSnapshot after) {
        java.util.List<String> changedFields = new java.util.ArrayList<>();
        
        if (!java.util.Objects.equals(before.getFirstName(), after.getFirstName())) {
            changedFields.add("firstName");
        }
        if (!java.util.Objects.equals(before.getLastName(), after.getLastName())) {
            changedFields.add("lastName");
        }
        if (!java.util.Objects.equals(before.getPhoneNumber(), after.getPhoneNumber())) {
            changedFields.add("phoneNumber");
        }
        if (!java.util.Objects.equals(before.getDateOfBirth(), after.getDateOfBirth())) {
            changedFields.add("dateOfBirth");
        }
        if (!java.util.Objects.equals(before.getCity(), after.getCity())) {
            changedFields.add("city");
        }
        if (!java.util.Objects.equals(before.getState(), after.getState())) {
            changedFields.add("state");
        }
        if (!java.util.Objects.equals(before.getProfileImageUrl(), after.getProfileImageUrl())) {
            changedFields.add("profileImageUrl");
        }
        
        return changedFields;
    }
}

/**
 * Event listener for handling user events locally
 */
@Component
@RequiredArgsConstructor
@Slf4j
class UserEventListener {
    
    private final UserNotificationService notificationService;
    private final UserAnalyticsService analyticsService;
    private final UserCacheManager cacheManager;
    
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT)
    public void handleUserRegisteredEvent(UserRegisteredEvent event) {
        log.info("Handling UserRegisteredEvent for user: {}", event.getUserId());
        
        // Send welcome notification
        notificationService.sendWelcomeNotification(event);
        
        // Update analytics
        analyticsService.recordUserRegistration(event);
        
        // Initialize user cache
        cacheManager.initializeUserCache(event.getUserId());
    }
    
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT)
    public void handleUserUpdatedEvent(UserUpdatedEvent event) {
        log.info("Handling UserUpdatedEvent for user: {}", event.getUserId());
        
        // Invalidate cache
        cacheManager.invalidateUser(event.getUserId());
        
        // Update analytics
        analyticsService.recordUserUpdate(event);
        
        // Send notification if significant changes
        if (hasSignificantChanges(event.getChangedFields())) {
            notificationService.sendProfileUpdateNotification(event);
        }
    }
    
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT)
    public void handleEmailVerifiedEvent(EmailVerifiedEvent event) {
        log.info("Handling EmailVerifiedEvent for user: {}", event.getUserId());
        
        // Send confirmation notification
        notificationService.sendEmailVerificationConfirmation(event);
        
        // Update analytics
        analyticsService.recordEmailVerification(event);
        
        // Update user cache
        cacheManager.updateUserVerificationStatus(event.getUserId(), true);
    }
    
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT)
    public void handlePasswordChangedEvent(PasswordChangedEvent event) {
        log.info("Handling PasswordChangedEvent for user: {}", event.getUserId());
        
        // Send security notification
        notificationService.sendPasswordChangeNotification(event);
        
        // Update analytics
        analyticsService.recordPasswordChange(event);
        
        // Invalidate all user sessions
        cacheManager.invalidateUserSessions(event.getUserId());
    }
    
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT)
    public void handleUserLockedEvent(UserLockedEvent event) {
        log.info("Handling UserLockedEvent for user: {}", event.getUserId());
        
        // Send account locked notification
        notificationService.sendAccountLockedNotification(event);
        
        // Update analytics
        analyticsService.recordUserLocked(event);
        
        // Invalidate all user sessions and cache
        cacheManager.invalidateUser(event.getUserId());
        cacheManager.invalidateUserSessions(event.getUserId());
    }
    
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT)
    public void handleUserLoginEvent(UserLoginEvent event) {
        log.debug("Handling UserLoginEvent for user: {}", event.getUserId());
        
        // Update analytics
        analyticsService.recordUserLogin(event);
        
        // Update last login cache
        cacheManager.updateLastLogin(event.getUserId(), event.getLoginAt());
    }
    
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT)
    public void handleUserLogoutEvent(UserLogoutEvent event) {
        log.debug("Handling UserLogoutEvent for user: {}", event.getUserId());
        
        // Update analytics
        analyticsService.recordUserLogout(event);
        
        // Clean up session cache
        cacheManager.removeSession(event.getSessionId());
    }
    
    private boolean hasSignificantChanges(java.util.List<String> changedFields) {
        java.util.Set<String> significantFields = java.util.Set.of("email", "phoneNumber", "firstName", "lastName");
        return changedFields.stream().anyMatch(significantFields::contains);
    }
}
