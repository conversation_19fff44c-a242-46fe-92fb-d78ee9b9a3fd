package com.wedding.vendor.model;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * Service entity representing services offered by vendors
 */
@Entity
@Table(name = "vendor_services", indexes = {
    @Index(name = "idx_service_vendor_id", columnList = "vendor_id"),
    @Index(name = "idx_service_category", columnList = "category"),
    @Index(name = "idx_service_active", columnList = "is_active"),
    @Index(name = "idx_service_featured", columnList = "is_featured"),
    @Index(name = "idx_service_price", columnList = "base_price")
})
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EntityListeners(AuditingEntityListener.class)
public class VendorService {
    
    @Id
    @GeneratedValue(strategy = GenerationType.UUID)
    private String id;
    
    @Column(name = "vendor_id", nullable = false)
    private String vendorId;
    
    @Column(nullable = false)
    private String name;
    
    @Column(name = "short_description")
    private String shortDescription;
    
    @Column(columnDefinition = "TEXT")
    private String description;
    
    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private Vendor.VendorCategory category;
    
    @Enumerated(EnumType.STRING)
    private Vendor.VendorSubcategory subcategory;
    
    @Column(name = "base_price", nullable = false, precision = 10, scale = 2)
    private BigDecimal basePrice;
    
    @Column(name = "max_price", precision = 10, scale = 2)
    private BigDecimal maxPrice;
    
    @Column(name = "price_unit")
    private String priceUnit; // per day, per hour, per event, per person, etc.
    
    @Column(name = "is_price_negotiable")
    private Boolean isPriceNegotiable = true;
    
    @Column(name = "duration_hours")
    private Integer durationHours;
    
    @Column(name = "duration_days")
    private Integer durationDays;
    
    @Column(name = "max_guests")
    private Integer maxGuests;
    
    @Column(name = "min_guests")
    private Integer minGuests;
    
    @Column(name = "advance_booking_days")
    private Integer advanceBookingDays = 7; // minimum days to book in advance
    
    @Column(name = "cancellation_policy", columnDefinition = "TEXT")
    private String cancellationPolicy;
    
    @Column(name = "refund_policy", columnDefinition = "TEXT")
    private String refundPolicy;
    
    @ElementCollection
    @CollectionTable(name = "service_features", joinColumns = @JoinColumn(name = "service_id"))
    @Column(name = "feature")
    private List<String> features;
    
    @ElementCollection
    @CollectionTable(name = "service_inclusions", joinColumns = @JoinColumn(name = "service_id"))
    @Column(name = "inclusion")
    private List<String> inclusions;
    
    @ElementCollection
    @CollectionTable(name = "service_exclusions", joinColumns = @JoinColumn(name = "service_id"))
    @Column(name = "exclusion")
    private List<String> exclusions;
    
    @ElementCollection
    @CollectionTable(name = "service_images", joinColumns = @JoinColumn(name = "service_id"))
    @Column(name = "image_url")
    private List<String> imageUrls;
    
    @Column(name = "video_url")
    private String videoUrl;
    
    @Column(name = "brochure_url")
    private String brochureUrl;
    
    @Column(name = "is_active")
    private Boolean isActive = true;
    
    @Column(name = "is_featured")
    private Boolean isFeatured = false;
    
    @Column(name = "is_popular")
    private Boolean isPopular = false;
    
    @Column(name = "booking_count")
    private Integer bookingCount = 0;
    
    @Column(name = "view_count")
    private Integer viewCount = 0;
    
    @Column(name = "inquiry_count")
    private Integer inquiryCount = 0;
    
    @Column(name = "rating_average", precision = 3, scale = 2)
    private BigDecimal ratingAverage = BigDecimal.ZERO;
    
    @Column(name = "review_count")
    private Integer reviewCount = 0;
    
    @Column(name = "availability_status")
    @Enumerated(EnumType.STRING)
    private AvailabilityStatus availabilityStatus = AvailabilityStatus.AVAILABLE;
    
    @ElementCollection
    @CollectionTable(name = "service_tags", joinColumns = @JoinColumn(name = "service_id"))
    @Column(name = "tag")
    private List<String> tags;
    
    @ElementCollection
    @CollectionTable(name = "service_locations", joinColumns = @JoinColumn(name = "service_id"))
    @Column(name = "location")
    private List<String> serviceLocations;
    
    @Column(name = "travel_charges", precision = 10, scale = 2)
    private BigDecimal travelCharges = BigDecimal.ZERO;
    
    @Column(name = "max_travel_distance_km")
    private Integer maxTravelDistanceKm;
    
    @Column(name = "setup_time_hours")
    private Integer setupTimeHours;
    
    @Column(name = "cleanup_time_hours")
    private Integer cleanupTimeHours;
    
    @Column(name = "equipment_provided")
    private Boolean equipmentProvided = false;
    
    @Column(name = "customization_available")
    private Boolean customizationAvailable = true;
    
    @Column(name = "trial_available")
    private Boolean trialAvailable = false;
    
    @Column(name = "trial_charges", precision = 10, scale = 2)
    private BigDecimal trialCharges = BigDecimal.ZERO;
    
    @Column(name = "advance_payment_percentage")
    private Integer advancePaymentPercentage = 25; // 25% default
    
    @Column(name = "terms_and_conditions", columnDefinition = "TEXT")
    private String termsAndConditions;
    
    @Column(name = "special_instructions", columnDefinition = "TEXT")
    private String specialInstructions;
    
    @Column(name = "seasonal_pricing")
    private Boolean seasonalPricing = false;
    
    @Column(name = "peak_season_multiplier", precision = 3, scale = 2)
    private BigDecimal peakSeasonMultiplier = new BigDecimal("1.00");
    
    @Column(name = "last_updated_by")
    private String lastUpdatedBy;
    
    @CreatedDate
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;
    
    @LastModifiedDate
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
    
    // Helper methods
    public BigDecimal getEffectivePrice(boolean isPeakSeason) {
        if (isPeakSeason && seasonalPricing) {
            return basePrice.multiply(peakSeasonMultiplier);
        }
        return basePrice;
    }
    
    public BigDecimal getTotalPrice(boolean isPeakSeason, BigDecimal additionalCharges) {
        BigDecimal effectivePrice = getEffectivePrice(isPeakSeason);
        return effectivePrice.add(additionalCharges != null ? additionalCharges : BigDecimal.ZERO);
    }
    
    public void incrementViewCount() {
        this.viewCount = (this.viewCount == null) ? 1 : this.viewCount + 1;
    }
    
    public void incrementInquiryCount() {
        this.inquiryCount = (this.inquiryCount == null) ? 1 : this.inquiryCount + 1;
    }
    
    public void incrementBookingCount() {
        this.bookingCount = (this.bookingCount == null) ? 1 : this.bookingCount + 1;
    }
    
    public boolean isAvailableForBooking() {
        return isActive && availabilityStatus == AvailabilityStatus.AVAILABLE;
    }
    
    // Enums
    public enum AvailabilityStatus {
        AVAILABLE, BUSY, TEMPORARILY_UNAVAILABLE, PERMANENTLY_UNAVAILABLE
    }
}
