package com.wedding.user.event;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.wedding.shared.exception.BusinessException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * Event Store implementation for Event Sourcing pattern
 * Stores all domain events for audit trail and event replay capabilities
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class UserEventStore {
    
    private final UserEventRepository eventRepository;
    private final ObjectMapper objectMapper;
    
    /**
     * Saves a domain event to the event store
     */
    @Transactional
    public void saveEvent(UserDomainEvent event) {
        try {
            String eventData = objectMapper.writeValueAsString(event);
            
            UserEventEntity eventEntity = UserEventEntity.builder()
                    .eventId(event.getEventId())
                    .aggregateId(event.getUserId())
                    .eventType(event.getClass().getSimpleName())
                    .eventData(eventData)
                    .eventVersion(1L)
                    .timestamp(event.getTimestamp())
                    .build();
            
            eventRepository.save(eventEntity);
            
            log.debug("Saved event: {} for aggregate: {}", event.getClass().getSimpleName(), event.getUserId());
            
        } catch (JsonProcessingException e) {
            log.error("Failed to serialize event: {}", event, e);
            throw new BusinessException("Failed to save event", "EVENT_SERIALIZATION_ERROR");
        }
    }
    
    /**
     * Retrieves all events for a specific user (aggregate)
     */
    public List<UserEventEntity> getEventsForUser(String userId) {
        return eventRepository.findByAggregateIdOrderByTimestampAsc(userId);
    }
    
    /**
     * Retrieves events for a user within a date range
     */
    public List<UserEventEntity> getEventsForUserInRange(String userId, LocalDateTime from, LocalDateTime to) {
        return eventRepository.findByAggregateIdAndTimestampBetweenOrderByTimestampAsc(userId, from, to);
    }
    
    /**
     * Retrieves events by type
     */
    public Page<UserEventEntity> getEventsByType(String eventType, Pageable pageable) {
        return eventRepository.findByEventTypeOrderByTimestampDesc(eventType, pageable);
    }
    
    /**
     * Retrieves the latest event for a user
     */
    public Optional<UserEventEntity> getLatestEventForUser(String userId) {
        return eventRepository.findFirstByAggregateIdOrderByTimestampDesc(userId);
    }
    
    /**
     * Counts total events for a user
     */
    public long countEventsForUser(String userId) {
        return eventRepository.countByAggregateId(userId);
    }
    
    /**
     * Retrieves events after a specific timestamp (for event replay)
     */
    public List<UserEventEntity> getEventsAfter(LocalDateTime timestamp, int limit) {
        return eventRepository.findByTimestampAfterOrderByTimestampAsc(timestamp, 
                org.springframework.data.domain.PageRequest.of(0, limit));
    }
    
    /**
     * Deserializes event data back to domain event
     */
    public <T extends UserDomainEvent> T deserializeEvent(UserEventEntity eventEntity, Class<T> eventClass) {
        try {
            return objectMapper.readValue(eventEntity.getEventData(), eventClass);
        } catch (JsonProcessingException e) {
            log.error("Failed to deserialize event: {}", eventEntity.getEventId(), e);
            throw new BusinessException("Failed to deserialize event", "EVENT_DESERIALIZATION_ERROR");
        }
    }
    
    /**
     * Rebuilds user state from events (Event Sourcing projection)
     */
    public UserProjection rebuildUserProjection(String userId) {
        List<UserEventEntity> events = getEventsForUser(userId);
        
        UserProjection projection = new UserProjection();
        projection.setUserId(userId);
        
        for (UserEventEntity eventEntity : events) {
            applyEventToProjection(projection, eventEntity);
        }
        
        return projection;
    }
    
    /**
     * Applies an event to a user projection
     */
    private void applyEventToProjection(UserProjection projection, UserEventEntity eventEntity) {
        try {
            switch (eventEntity.getEventType()) {
                case "UserRegisteredEvent":
                    UserRegisteredEvent registeredEvent = deserializeEvent(eventEntity, UserRegisteredEvent.class);
                    projection.setEmail(registeredEvent.getEmail());
                    projection.setFirstName(registeredEvent.getFirstName());
                    projection.setLastName(registeredEvent.getLastName());
                    projection.setRegisteredAt(registeredEvent.getTimestamp());
                    projection.setActive(true);
                    projection.setVerified(false);
                    break;
                    
                case "EmailVerifiedEvent":
                    projection.setVerified(true);
                    projection.setVerifiedAt(eventEntity.getTimestamp());
                    break;
                    
                case "UserUpdatedEvent":
                    UserUpdatedEvent updatedEvent = deserializeEvent(eventEntity, UserUpdatedEvent.class);
                    if (updatedEvent.getAfterSnapshot() != null) {
                        projection.setFirstName(updatedEvent.getAfterSnapshot().getFirstName());
                        projection.setLastName(updatedEvent.getAfterSnapshot().getLastName());
                        projection.setPhoneNumber(updatedEvent.getAfterSnapshot().getPhoneNumber());
                        projection.setCity(updatedEvent.getAfterSnapshot().getCity());
                        projection.setState(updatedEvent.getAfterSnapshot().getState());
                    }
                    projection.setLastUpdatedAt(eventEntity.getTimestamp());
                    break;
                    
                case "PasswordChangedEvent":
                    projection.setLastPasswordChangeAt(eventEntity.getTimestamp());
                    break;
                    
                case "UserLockedEvent":
                    projection.setActive(false);
                    projection.setLockedAt(eventEntity.getTimestamp());
                    break;
                    
                case "UserLoginEvent":
                    projection.setLastLoginAt(eventEntity.getTimestamp());
                    projection.setLoginCount(projection.getLoginCount() + 1);
                    break;
                    
                default:
                    log.debug("Unknown event type for projection: {}", eventEntity.getEventType());
            }
        } catch (Exception e) {
            log.error("Failed to apply event to projection: {}", eventEntity.getEventId(), e);
        }
    }
}

/**
 * JPA Entity for storing events
 */
@Entity
@Table(name = "user_events", indexes = {
    @Index(name = "idx_user_events_aggregate_id", columnList = "aggregate_id"),
    @Index(name = "idx_user_events_event_type", columnList = "event_type"),
    @Index(name = "idx_user_events_timestamp", columnList = "timestamp")
})
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
class UserEventEntity {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "event_id", nullable = false, unique = true)
    private String eventId;
    
    @Column(name = "aggregate_id", nullable = false)
    private String aggregateId;
    
    @Column(name = "event_type", nullable = false)
    private String eventType;
    
    @Column(name = "event_data", nullable = false, columnDefinition = "TEXT")
    private String eventData;
    
    @Column(name = "event_version", nullable = false)
    private Long eventVersion;
    
    @Column(name = "timestamp", nullable = false)
    private LocalDateTime timestamp;
    
    @Column(name = "created_at", nullable = false)
    @Builder.Default
    private LocalDateTime createdAt = LocalDateTime.now();
}

/**
 * Repository for user events
 */
@org.springframework.data.jpa.repository.JpaRepository
interface UserEventRepository extends org.springframework.data.jpa.repository.JpaRepository<UserEventEntity, Long> {
    
    List<UserEventEntity> findByAggregateIdOrderByTimestampAsc(String aggregateId);
    
    List<UserEventEntity> findByAggregateIdAndTimestampBetweenOrderByTimestampAsc(
            String aggregateId, LocalDateTime from, LocalDateTime to);
    
    Page<UserEventEntity> findByEventTypeOrderByTimestampDesc(String eventType, Pageable pageable);
    
    Optional<UserEventEntity> findFirstByAggregateIdOrderByTimestampDesc(String aggregateId);
    
    long countByAggregateId(String aggregateId);
    
    List<UserEventEntity> findByTimestampAfterOrderByTimestampAsc(LocalDateTime timestamp, Pageable pageable);
    
    @org.springframework.data.jpa.repository.Query("SELECT COUNT(DISTINCT e.aggregateId) FROM UserEventEntity e WHERE e.eventType = :eventType")
    long countUniqueAggregatesByEventType(@org.springframework.data.repository.query.Param("eventType") String eventType);
    
    @org.springframework.data.jpa.repository.Query("SELECT e FROM UserEventEntity e WHERE e.timestamp >= :from AND e.timestamp <= :to ORDER BY e.timestamp ASC")
    List<UserEventEntity> findEventsInTimeRange(
            @org.springframework.data.repository.query.Param("from") LocalDateTime from,
            @org.springframework.data.repository.query.Param("to") LocalDateTime to);
}

/**
 * User projection built from events
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
class UserProjection {
    private String userId;
    private String email;
    private String firstName;
    private String lastName;
    private String phoneNumber;
    private String city;
    private String state;
    private boolean active;
    private boolean verified;
    private LocalDateTime registeredAt;
    private LocalDateTime verifiedAt;
    private LocalDateTime lastUpdatedAt;
    private LocalDateTime lastPasswordChangeAt;
    private LocalDateTime lastLoginAt;
    private LocalDateTime lockedAt;
    private int loginCount;
}
