package com.wedding.gateway.controller;

import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * Fallback controller for circuit breaker responses
 */
@RestController
@RequestMapping("/fallback")
@Slf4j
public class FallbackController {
    
    @GetMapping("/user-service")
    @PostMapping("/user-service")
    public ResponseEntity<Map<String, Object>> userServiceFallback() {
        log.warn("User service is currently unavailable - returning fallback response");
        return createFallbackResponse("User Service", "User operations are temporarily unavailable");
    }
    
    @GetMapping("/vendor-service")
    @PostMapping("/vendor-service")
    public ResponseEntity<Map<String, Object>> vendorServiceFallback() {
        log.warn("Vendor service is currently unavailable - returning fallback response");
        return createFallbackResponse("Vendor Service", "Vendor operations are temporarily unavailable");
    }
    
    @GetMapping("/booking-service")
    @PostMapping("/booking-service")
    public ResponseEntity<Map<String, Object>> bookingServiceFallback() {
        log.warn("Booking service is currently unavailable - returning fallback response");
        return createFallbackResponse("Booking Service", "Booking operations are temporarily unavailable");
    }
    
    @GetMapping("/search-service")
    @PostMapping("/search-service")
    public ResponseEntity<Map<String, Object>> searchServiceFallback() {
        log.warn("Search service is currently unavailable - returning fallback response");
        return createFallbackResponse("Search Service", "Search functionality is temporarily unavailable");
    }
    
    @GetMapping("/payment-service")
    @PostMapping("/payment-service")
    public ResponseEntity<Map<String, Object>> paymentServiceFallback() {
        log.warn("Payment service is currently unavailable - returning fallback response");
        return createFallbackResponse("Payment Service", "Payment operations are temporarily unavailable");
    }
    
    @GetMapping("/admin-service")
    @PostMapping("/admin-service")
    public ResponseEntity<Map<String, Object>> adminServiceFallback() {
        log.warn("Admin service is currently unavailable - returning fallback response");
        return createFallbackResponse("Admin Service", "Admin operations are temporarily unavailable");
    }
    
    @GetMapping("/analytics-service")
    @PostMapping("/analytics-service")
    public ResponseEntity<Map<String, Object>> analyticsServiceFallback() {
        log.warn("Analytics service is currently unavailable - returning fallback response");
        return createFallbackResponse("Analytics Service", "Analytics are temporarily unavailable");
    }
    
    @GetMapping("/messaging-service")
    @PostMapping("/messaging-service")
    public ResponseEntity<Map<String, Object>> messagingServiceFallback() {
        log.warn("Messaging service is currently unavailable - returning fallback response");
        return createFallbackResponse("Messaging Service", "Messaging operations are temporarily unavailable");
    }
    
    private ResponseEntity<Map<String, Object>> createFallbackResponse(String serviceName, String message) {
        Map<String, Object> response = new HashMap<>();
        response.put("success", false);
        response.put("message", message);
        response.put("errorCode", "SERVICE_UNAVAILABLE");
        response.put("service", serviceName);
        response.put("timestamp", LocalDateTime.now());
        response.put("fallback", true);
        
        Map<String, Object> data = new HashMap<>();
        data.put("retryAfter", "30 seconds");
        data.put("supportContact", "<EMAIL>");
        response.put("data", data);
        
        return ResponseEntity.status(HttpStatus.SERVICE_UNAVAILABLE).body(response);
    }
}
