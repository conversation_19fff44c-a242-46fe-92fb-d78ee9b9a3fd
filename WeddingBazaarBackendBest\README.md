# WeddingBazaar Backend - Enterprise Microservices Architecture

## 🏗️ Architecture Overview

This project implements a comprehensive **enterprise-grade microservices architecture** for a wedding marketplace platform using modern patterns and technologies.

### 🚀 Tech Stack
- **Java 17+** with Spring Boot 3.x
- **Spring Cloud** for microservices infrastructure (Eureka, Gateway, Config)
- **PostgreSQL** for relational data (ACID compliance)
- **MongoDB** for flexible document storage
- **Elasticsearch** for full-text search and analytics
- **Redis** for caching and session management
- **Apache Kafka** for event streaming and messaging
- **Docker & Kubernetes** for containerization
- **OAuth2/JWT** for security and authentication
- **Stripe/Razorpay** for payment processing
- **Prometheus/Grafana** for monitoring
- **ELK Stack** for logging

### 🎯 Microservices Architecture

| Service | Port | Description | Database |
|---------|------|-------------|----------|
| **Eureka Server** | 8761 | Service Discovery | - |
| **Config Server** | 8888 | Centralized Configuration | - |
| **API Gateway** | 8080 | Entry Point, Routing, Security | Redis |
| **User Service** | 8081 | Authentication, User Management | PostgreSQL |
| **Vendor Service** | 8082 | Vendor Registration, Profiles | PostgreSQL + MongoDB |
| **Booking Service** | 8083 | Booking Workflow, State Management | PostgreSQL |
| **Search Service** | 8084 | Full-text Search, Recommendations | Elasticsearch |
| **Payment Service** | 8085 | Payment Processing, Stripe/Razorpay | PostgreSQL |
| **Messaging Service** | 8086 | Real-time Chat, Notifications | MongoDB |
| **Analytics Service** | 8087 | Business Intelligence, Metrics | PostgreSQL |
| **Admin Service** | 8088 | Admin Dashboard, Management | PostgreSQL |
| **Notification Service** | 8089 | Email, SMS, Push Notifications | MongoDB |

### 🏛️ Enterprise Patterns

- **CQRS (Command Query Responsibility Segregation)** - Separate read/write models
- **Event Sourcing** - Complete audit trail of all changes
- **Saga Pattern** - Distributed transaction management
- **Circuit Breaker** - Fault tolerance and resilience
- **API Gateway Pattern** - Single entry point with cross-cutting concerns
- **Database per Service** - Data isolation and independence
- **Event-Driven Architecture** - Loose coupling via events

### 🔧 Infrastructure Components

- **Service Discovery** - Netflix Eureka
- **Configuration Management** - Spring Cloud Config
- **Load Balancing** - Spring Cloud Gateway
- **Message Broker** - Apache Kafka
- **Caching** - Redis
- **Search Engine** - Elasticsearch
- **Monitoring** - Prometheus + Grafana
- **Logging** - ELK Stack (Elasticsearch, Logstash, Kibana)
- **Tracing** - Jaeger

## 🚀 Quick Start

### Prerequisites
- **Java 17+**
- **Maven 3.8+**
- **Docker & Docker Compose** (optional but recommended)
- **Git**

### Test Your Setup First
Before running the application, test if all prerequisites are installed:

**Linux/Mac:**
```bash
./test-setup.sh
```

**Windows:**
```batch
test-setup.bat
```

### Option 1: Quick Start (Recommended)

**Linux/Mac:**
```bash
./quick-start.sh
```

**Windows:**
```batch
run-application.bat
```

### Option 2: Manual Setup

#### 1. Start Infrastructure Services
```bash
# Using Docker (Recommended)
docker run -d --name postgres-wedding \
  -e POSTGRES_DB=wedding_bazaar \
  -e POSTGRES_USER=postgres \
  -e POSTGRES_PASSWORD=postgres \
  -p 5432:5432 postgres:15

docker run -d --name redis-wedding \
  -p 6379:6379 redis:7-alpine
```

#### 2. Build the Project
```bash
mvn clean install -DskipTests
```

#### 3. Start Services in Order
```bash
# Terminal 1 - Service Registry
cd service-registry && mvn spring-boot:run -Dspring.profiles.active=dev

# Terminal 2 - Config Server
cd config-server && mvn spring-boot:run -Dspring.profiles.active=dev

# Terminal 3 - API Gateway
cd api-gateway && mvn spring-boot:run -Dspring.profiles.active=dev

# Terminal 4 - User Service
cd user-service && mvn spring-boot:run -Dspring.profiles.active=dev
```

### 4. Verify Installation
- **Eureka Dashboard**: http://localhost:8761
- **API Gateway**: http://localhost:8080
- **Swagger UI**: http://localhost:8080/swagger-ui.html
- **Config Server**: http://localhost:8888

### 📚 Detailed Guides
- **[Installation Guide](INSTALLATION_GUIDE.md)** - Complete setup instructions
- **[Startup Guide](STARTUP_GUIDE.md)** - Detailed running instructions

## 📊 Service Health Check
```bash
# Check all services health
curl http://localhost:8080/actuator/health

# Individual service health
curl http://localhost:8081/actuator/health  # User Service
curl http://localhost:8082/actuator/health  # Vendor Service
curl http://localhost:8083/actuator/health  # Booking Service
```

## 🔗 API Endpoints

### Authentication APIs
```bash
# User Registration
POST http://localhost:8080/api/auth/register
Content-Type: application/json
{
  "email": "<EMAIL>",
  "password": "password123",
  "firstName": "John",
  "lastName": "Doe",
  "role": "CUSTOMER",
  "city": "Mumbai",
  "state": "Maharashtra"
}

# User Login
POST http://localhost:8080/api/auth/login
Content-Type: application/json
{
  "email": "<EMAIL>",
  "password": "password123"
}
```

### Vendor APIs
```bash
# Search Vendors
GET http://localhost:8080/api/search/vendors?query=photography&city=Mumbai&category=PHOTOGRAPHER

# Get Vendor Details
GET http://localhost:8080/api/vendors/{vendorId}

# Create Vendor Profile
POST http://localhost:8080/api/vendors
Authorization: Bearer {token}
Content-Type: application/json
{
  "businessName": "Perfect Moments Photography",
  "category": "PHOTOGRAPHER",
  "city": "Mumbai",
  "state": "Maharashtra",
  "description": "Professional wedding photography services"
}
```

### Booking APIs
```bash
# Create Booking
POST http://localhost:8080/api/bookings
Authorization: Bearer {token}
Content-Type: application/json
{
  "vendorId": "vendor-123",
  "serviceId": "service-456",
  "eventDate": "2024-06-15T10:00:00",
  "eventLocation": "Mumbai",
  "guestCount": 200,
  "totalAmount": 50000
}

# Get User Bookings
GET http://localhost:8080/api/bookings/user/{userId}
Authorization: Bearer {token}
```

### Payment APIs
```bash
# Create Payment Session
POST http://localhost:8080/api/payments/create-session
Authorization: Bearer {token}
Content-Type: application/json
{
  "bookingId": "booking-123",
  "amount": 50000,
  "currency": "INR",
  "description": "Wedding Photography Service"
}
```

## 🗄️ Database Schema

### Core Entities

#### Users Table (PostgreSQL)
```sql
CREATE TABLE users (
    id UUID PRIMARY KEY,
    email VARCHAR(255) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    phone_number VARCHAR(15),
    role VARCHAR(20) NOT NULL,
    is_active BOOLEAN DEFAULT true,
    is_verified BOOLEAN DEFAULT false,
    city VARCHAR(100),
    state VARCHAR(100),
    country VARCHAR(100) DEFAULT 'India',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### Vendors Table (PostgreSQL)
```sql
CREATE TABLE vendors (
    id UUID PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES users(id),
    business_name VARCHAR(255) NOT NULL,
    category VARCHAR(50) NOT NULL,
    city VARCHAR(100) NOT NULL,
    state VARCHAR(100) NOT NULL,
    rating_average DECIMAL(3,2) DEFAULT 0.00,
    total_reviews INTEGER DEFAULT 0,
    is_verified BOOLEAN DEFAULT false,
    is_active BOOLEAN DEFAULT true,
    starting_price DECIMAL(10,2),
    max_price DECIMAL(10,2),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### Bookings Table (PostgreSQL)
```sql
CREATE TABLE bookings (
    id UUID PRIMARY KEY,
    user_id UUID NOT NULL,
    vendor_id UUID NOT NULL,
    service_id UUID NOT NULL,
    status VARCHAR(20) NOT NULL DEFAULT 'PENDING',
    event_date TIMESTAMP NOT NULL,
    event_location VARCHAR(255) NOT NULL,
    total_amount DECIMAL(10,2) NOT NULL,
    payment_status VARCHAR(20) DEFAULT 'PENDING',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### Payments Table (PostgreSQL)
```sql
CREATE TABLE payments (
    id UUID PRIMARY KEY,
    booking_id UUID NOT NULL REFERENCES bookings(id),
    user_id UUID NOT NULL,
    vendor_id UUID NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'INR',
    payment_status VARCHAR(20) NOT NULL DEFAULT 'PENDING',
    payment_method VARCHAR(20),
    provider_payment_id VARCHAR(255),
    transaction_id VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## 🔧 Configuration

### Environment Variables
```bash
# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=wedding_bazaar
DB_USERNAME=postgres
DB_PASSWORD=postgres

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379

# Kafka Configuration
KAFKA_BOOTSTRAP_SERVERS=localhost:9092

# Elasticsearch Configuration
ELASTICSEARCH_HOST=localhost
ELASTICSEARCH_PORT=9200

# JWT Configuration
JWT_SECRET=your-secret-key
JWT_EXPIRATION=86400

# Stripe Configuration
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret

# Email Configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-app-password
```
