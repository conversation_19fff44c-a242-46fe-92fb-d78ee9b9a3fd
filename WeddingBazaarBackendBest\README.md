# WeddingBazaar Backend - Microservices Architecture

## Architecture Overview

This project implements a comprehensive microservices architecture for a wedding marketplace platform using modern enterprise patterns and technologies.

### Tech Stack
- **Java 17+** with Spring Boot 3.x
- **Spring Cloud** for microservices infrastructure
- **PostgreSQL** for relational data
- **MongoDB** for flexible document storage
- **Elasticsearch** for search capabilities
- **Redis** for caching
- **Apache Kafka** for event streaming
- **Docker & Kubernetes** for containerization
- **OAuth2/JWT** for security

### Microservices

1. **API Gateway Service** - Central entry point with routing, authentication, and rate limiting
2. **User Service** - User management, authentication, and profiles
3. **Vendor Service** - Vendor registration, verification, and management
4. **Booking Service** - Booking workflow and management
5. **Search Service** - Full-text search with Elasticsearch
6. **Messaging Service** - Real-time chat and notifications
7. **Analytics Service** - Metrics and business intelligence
8. **Admin Service** - Administrative operations and dashboard
9. **Payment Service** - Payment processing and transactions
10. **Notification Service** - Email, SMS, and push notifications

### Key Features

- **CQRS Pattern** for read/write separation
- **Event Sourcing** for audit trails
- **Circuit Breaker** for fault tolerance
- **Distributed Tracing** for observability
- **Auto-scaling** capabilities
- **Blue-Green Deployment** support

### Getting Started

1. Clone the repository
2. Run `docker-compose up` to start infrastructure services
3. Build and run individual microservices
4. Access API Gateway at `http://localhost:8080`

### Documentation

- [API Documentation](./docs/api.md)
- [Database Schema](./docs/database.md)
- [Deployment Guide](./docs/deployment.md)
- [Architecture Decisions](./docs/architecture.md)
