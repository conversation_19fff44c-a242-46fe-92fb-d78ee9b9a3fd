package com.weddingbazaar.payment;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.netflix.eureka.EnableEurekaClient;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import org.springframework.kafka.annotation.EnableKafka;

/**
 * Payment Service Application
 */
@SpringBootApplication(scanBasePackages = {"com.weddingbazaar.payment", "com.weddingbazaar.common"})
@EnableEurekaClient
@EnableJpaAuditing
@EnableKafka
public class PaymentServiceApplication {
    
    public static void main(String[] args) {
        SpringApplication.run(PaymentServiceApplication.class, args);
    }
}
