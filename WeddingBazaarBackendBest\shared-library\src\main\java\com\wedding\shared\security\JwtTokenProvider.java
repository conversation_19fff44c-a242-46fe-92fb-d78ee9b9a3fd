package com.wedding.shared.security;

import com.auth0.jwt.JWT;
import com.auth0.jwt.JWTVerifier;
import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.exceptions.JWTVerificationException;
import com.auth0.jwt.interfaces.DecodedJWT;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.stereotype.Component;

import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Date;
import java.util.stream.Collectors;

/**
 * Enhanced JWT token provider with comprehensive token management
 */
@Component
@Slf4j
public class JwtTokenProvider {
    
    @Value("${jwt.secret:wedding-bazaar-secret-key-2024}")
    private String secretKey;
    
    @Value("${jwt.expiration:86400}") // 24 hours in seconds
    private long jwtExpiration;
    
    @Value("${jwt.refresh-expiration:604800}") // 7 days in seconds
    private long refreshExpiration;
    
    @Value("${jwt.issuer:wedding-bazaar}")
    private String issuer;
    
    private Algorithm getAlgorithm() {
        return Algorithm.HMAC256(secretKey);
    }
    
    public String generateToken(Authentication authentication) {
        UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
        return generateToken(userPrincipal);
    }
    
    public String generateToken(UserPrincipal userPrincipal) {
        String authorities = userPrincipal.getAuthorities().stream()
                .map(GrantedAuthority::getAuthority)
                .collect(Collectors.joining(","));
        
        return JWT.create()
                .withSubject(userPrincipal.getId())
                .withClaim("email", userPrincipal.getEmail())
                .withClaim("firstName", userPrincipal.getFirstName())
                .withClaim("lastName", userPrincipal.getLastName())
                .withClaim("authorities", authorities)
                .withClaim("verified", userPrincipal.isVerified())
                .withIssuedAt(new Date())
                .withExpiresAt(Date.from(Instant.now().plus(jwtExpiration, ChronoUnit.SECONDS)))
                .withIssuer(issuer)
                .withClaim("type", "access")
                .sign(getAlgorithm());
    }
    
    public String generateRefreshToken(UserPrincipal userPrincipal) {
        return JWT.create()
                .withSubject(userPrincipal.getId())
                .withClaim("email", userPrincipal.getEmail())
                .withIssuedAt(new Date())
                .withExpiresAt(Date.from(Instant.now().plus(refreshExpiration, ChronoUnit.SECONDS)))
                .withIssuer(issuer)
                .withClaim("type", "refresh")
                .sign(getAlgorithm());
    }
    
    public boolean validateToken(String token) {
        try {
            JWTVerifier verifier = JWT.require(getAlgorithm())
                    .withIssuer(issuer)
                    .build();
            verifier.verify(token);
            return true;
        } catch (JWTVerificationException e) {
            log.debug("JWT validation failed: {}", e.getMessage());
            return false;
        }
    }
    
    public String getUserIdFromToken(String token) {
        try {
            DecodedJWT decodedJWT = JWT.decode(token);
            return decodedJWT.getSubject();
        } catch (Exception e) {
            log.error("Error extracting user ID from token: {}", e.getMessage());
            return null;
        }
    }
    
    public String getEmailFromToken(String token) {
        try {
            DecodedJWT decodedJWT = JWT.decode(token);
            return decodedJWT.getClaim("email").asString();
        } catch (Exception e) {
            log.error("Error extracting email from token: {}", e.getMessage());
            return null;
        }
    }
    
    public String getAuthoritiesFromToken(String token) {
        try {
            DecodedJWT decodedJWT = JWT.decode(token);
            return decodedJWT.getClaim("authorities").asString();
        } catch (Exception e) {
            log.error("Error extracting authorities from token: {}", e.getMessage());
            return null;
        }
    }
    
    public boolean isTokenExpired(String token) {
        try {
            DecodedJWT decodedJWT = JWT.decode(token);
            return decodedJWT.getExpiresAt().before(new Date());
        } catch (Exception e) {
            log.error("Error checking token expiration: {}", e.getMessage());
            return true;
        }
    }
    
    public boolean isRefreshToken(String token) {
        try {
            DecodedJWT decodedJWT = JWT.decode(token);
            String type = decodedJWT.getClaim("type").asString();
            return "refresh".equals(type);
        } catch (Exception e) {
            return false;
        }
    }
    
    public boolean isAccessToken(String token) {
        try {
            DecodedJWT decodedJWT = JWT.decode(token);
            String type = decodedJWT.getClaim("type").asString();
            return "access".equals(type);
        } catch (Exception e) {
            return false;
        }
    }
    
    public UserPrincipal getUserPrincipalFromToken(String token) {
        try {
            DecodedJWT decodedJWT = JWT.decode(token);
            
            return UserPrincipal.builder()
                    .id(decodedJWT.getSubject())
                    .email(decodedJWT.getClaim("email").asString())
                    .firstName(decodedJWT.getClaim("firstName").asString())
                    .lastName(decodedJWT.getClaim("lastName").asString())
                    .verified(decodedJWT.getClaim("verified").asBoolean())
                    .authorities(decodedJWT.getClaim("authorities").asString())
                    .build();
        } catch (Exception e) {
            log.error("Error extracting user principal from token: {}", e.getMessage());
            return null;
        }
    }
    
    public long getExpirationTime() {
        return jwtExpiration;
    }
    
    public long getRefreshExpirationTime() {
        return refreshExpiration;
    }
}
