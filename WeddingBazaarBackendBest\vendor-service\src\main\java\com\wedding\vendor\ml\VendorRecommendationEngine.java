package com.wedding.vendor.ml;

import com.wedding.vendor.dto.VendorRecommendationRequest;
import com.wedding.vendor.model.Vendor;
import com.wedding.vendor.repository.VendorRepository;
import com.wedding.vendor.analytics.VendorAnalyticsService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Advanced ML-based vendor recommendation engine
 * Implements collaborative filtering, content-based filtering, and hybrid approaches
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class VendorRecommendationEngine {
    
    private final VendorRepository vendorRepository;
    private final VendorAnalyticsService analyticsService;
    private final UserBehaviorAnalyzer userBehaviorAnalyzer;
    private final VendorSimilarityCalculator similarityCalculator;
    private final RecommendationModelTrainer modelTrainer;
    
    /**
     * Generates vendor recommendations using hybrid ML approach
     */
    public List<String> getRecommendations(RecommendationContext context) {
        log.debug("Generating recommendations for user: {}", context.getUserId());
        
        try {
            // Get user behavior profile
            UserBehaviorProfile userProfile = userBehaviorAnalyzer.analyzeUserBehavior(context.getUserId());
            
            // Apply different recommendation strategies
            List<RecommendationResult> collaborativeResults = getCollaborativeFilteringRecommendations(userProfile, context);
            List<RecommendationResult> contentBasedResults = getContentBasedRecommendations(userProfile, context);
            List<RecommendationResult> popularityResults = getPopularityBasedRecommendations(context);
            List<RecommendationResult> locationResults = getLocationBasedRecommendations(context);
            
            // Combine and weight the results
            List<RecommendationResult> hybridResults = combineRecommendations(
                    collaborativeResults, contentBasedResults, popularityResults, locationResults, userProfile);
            
            // Apply business rules and filters
            List<RecommendationResult> filteredResults = applyBusinessRules(hybridResults, context);
            
            // Sort by final score and return top N
            return filteredResults.stream()
                    .sorted((a, b) -> Double.compare(b.getScore(), a.getScore()))
                    .limit(context.getMaxResults())
                    .map(RecommendationResult::getVendorId)
                    .collect(Collectors.toList());
                    
        } catch (Exception e) {
            log.error("Error generating recommendations for user: {}", context.getUserId(), e);
            return getFallbackRecommendations(context);
        }
    }
    
    /**
     * Collaborative filtering based on user-vendor interactions
     */
    private List<RecommendationResult> getCollaborativeFilteringRecommendations(
            UserBehaviorProfile userProfile, RecommendationContext context) {
        
        // Find similar users based on behavior patterns
        List<String> similarUsers = userBehaviorAnalyzer.findSimilarUsers(context.getUserId(), 50);
        
        // Get vendors liked by similar users
        Map<String, Double> vendorScores = new HashMap<>();
        
        for (String similarUserId : similarUsers) {
            UserBehaviorProfile similarUserProfile = userBehaviorAnalyzer.analyzeUserBehavior(similarUserId);
            double userSimilarity = calculateUserSimilarity(userProfile, similarUserProfile);
            
            // Weight vendor scores by user similarity
            for (Map.Entry<String, Double> entry : similarUserProfile.getVendorPreferences().entrySet()) {
                String vendorId = entry.getKey();
                double preference = entry.getValue();
                
                vendorScores.merge(vendorId, userSimilarity * preference, Double::sum);
            }
        }
        
        return vendorScores.entrySet().stream()
                .map(entry -> new RecommendationResult(entry.getKey(), entry.getValue(), "COLLABORATIVE"))
                .collect(Collectors.toList());
    }
    
    /**
     * Content-based filtering using vendor features
     */
    private List<RecommendationResult> getContentBasedRecommendations(
            UserBehaviorProfile userProfile, RecommendationContext context) {
        
        // Get user's preferred vendor features
        Map<String, Double> featurePreferences = userProfile.getFeaturePreferences();
        
        // Find vendors matching user preferences
        List<Vendor> candidates = vendorRepository.findActiveVendorsByCategory(context.getRequest().getCategory());
        
        List<RecommendationResult> results = new ArrayList<>();
        
        for (Vendor vendor : candidates) {
            double score = calculateContentBasedScore(vendor, featurePreferences, context);
            if (score > 0.3) { // Minimum threshold
                results.add(new RecommendationResult(vendor.getId(), score, "CONTENT_BASED"));
            }
        }
        
        return results;
    }
    
    /**
     * Popularity-based recommendations
     */
    private List<RecommendationResult> getPopularityBasedRecommendations(RecommendationContext context) {
        List<Vendor> popularVendors = vendorRepository.findTopRatedVendorsByCategory(
                context.getRequest().getCategory(), 20);
        
        return popularVendors.stream()
                .map(vendor -> {
                    double score = calculatePopularityScore(vendor);
                    return new RecommendationResult(vendor.getId(), score, "POPULARITY");
                })
                .collect(Collectors.toList());
    }
    
    /**
     * Location-based recommendations
     */
    private List<RecommendationResult> getLocationBasedRecommendations(RecommendationContext context) {
        if (context.getRequest().getLatitude() == null || context.getRequest().getLongitude() == null) {
            return Collections.emptyList();
        }
        
        List<Vendor> nearbyVendors = vendorRepository.findVendorsNearLocation(
                context.getRequest().getLatitude(),
                context.getRequest().getLongitude(),
                context.getRequest().getMaxDistanceKm() != null ? context.getRequest().getMaxDistanceKm() : 50.0,
                50);
        
        return nearbyVendors.stream()
                .map(vendor -> {
                    double distance = calculateDistance(
                            context.getRequest().getLatitude(), context.getRequest().getLongitude(),
                            vendor.getLatitude(), vendor.getLongitude());
                    double score = calculateLocationScore(distance);
                    return new RecommendationResult(vendor.getId(), score, "LOCATION");
                })
                .collect(Collectors.toList());
    }
    
    /**
     * Combines multiple recommendation strategies using weighted scoring
     */
    private List<RecommendationResult> combineRecommendations(
            List<RecommendationResult> collaborative,
            List<RecommendationResult> contentBased,
            List<RecommendationResult> popularity,
            List<RecommendationResult> location,
            UserBehaviorProfile userProfile) {
        
        // Dynamic weights based on user profile
        double collaborativeWeight = userProfile.getInteractionCount() > 10 ? 0.4 : 0.2;
        double contentWeight = 0.3;
        double popularityWeight = userProfile.getInteractionCount() < 5 ? 0.3 : 0.2;
        double locationWeight = 0.2;
        
        Map<String, Double> combinedScores = new HashMap<>();
        Map<String, Set<String>> vendorSources = new HashMap<>();
        
        // Combine scores from different strategies
        addWeightedScores(combinedScores, vendorSources, collaborative, collaborativeWeight);
        addWeightedScores(combinedScores, vendorSources, contentBased, contentWeight);
        addWeightedScores(combinedScores, vendorSources, popularity, popularityWeight);
        addWeightedScores(combinedScores, vendorSources, location, locationWeight);
        
        return combinedScores.entrySet().stream()
                .map(entry -> {
                    String vendorId = entry.getKey();
                    double score = entry.getValue();
                    String sources = String.join(",", vendorSources.get(vendorId));
                    return new RecommendationResult(vendorId, score, sources);
                })
                .collect(Collectors.toList());
    }
    
    /**
     * Applies business rules and filters
     */
    private List<RecommendationResult> applyBusinessRules(
            List<RecommendationResult> results, RecommendationContext context) {
        
        return results.stream()
                .filter(result -> {
                    // Filter by price range
                    if (context.getRequest().getMinPrice() != null || context.getRequest().getMaxPrice() != null) {
                        Optional<Vendor> vendor = vendorRepository.findById(result.getVendorId());
                        if (vendor.isPresent()) {
                            BigDecimal price = vendor.get().getStartingPrice();
                            if (context.getRequest().getMinPrice() != null && 
                                price.compareTo(context.getRequest().getMinPrice()) < 0) {
                                return false;
                            }
                            if (context.getRequest().getMaxPrice() != null && 
                                price.compareTo(context.getRequest().getMaxPrice()) > 0) {
                                return false;
                            }
                        }
                    }
                    
                    // Filter by availability
                    if (context.getRequest().getEventDate() != null) {
                        // Check vendor availability for the event date
                        return isVendorAvailable(result.getVendorId(), context.getRequest().getEventDate());
                    }
                    
                    return true;
                })
                .map(result -> {
                    // Apply business boosts
                    double boostedScore = applyBusinessBoosts(result, context);
                    return new RecommendationResult(result.getVendorId(), boostedScore, result.getSource());
                })
                .collect(Collectors.toList());
    }
    
    /**
     * Calculates user similarity based on behavior patterns
     */
    private double calculateUserSimilarity(UserBehaviorProfile profile1, UserBehaviorProfile profile2) {
        // Cosine similarity between vendor preference vectors
        Map<String, Double> prefs1 = profile1.getVendorPreferences();
        Map<String, Double> prefs2 = profile2.getVendorPreferences();
        
        Set<String> commonVendors = new HashSet<>(prefs1.keySet());
        commonVendors.retainAll(prefs2.keySet());
        
        if (commonVendors.isEmpty()) {
            return 0.0;
        }
        
        double dotProduct = 0.0;
        double norm1 = 0.0;
        double norm2 = 0.0;
        
        for (String vendorId : commonVendors) {
            double pref1 = prefs1.get(vendorId);
            double pref2 = prefs2.get(vendorId);
            
            dotProduct += pref1 * pref2;
            norm1 += pref1 * pref1;
            norm2 += pref2 * pref2;
        }
        
        if (norm1 == 0.0 || norm2 == 0.0) {
            return 0.0;
        }
        
        return dotProduct / (Math.sqrt(norm1) * Math.sqrt(norm2));
    }
    
    /**
     * Calculates content-based score for a vendor
     */
    private double calculateContentBasedScore(Vendor vendor, Map<String, Double> featurePreferences, RecommendationContext context) {
        double score = 0.0;
        
        // Category match
        if (vendor.getCategory().name().equals(context.getRequest().getCategory())) {
            score += 0.3;
        }
        
        // Rating score
        if (vendor.getRatingAverage() != null) {
            score += vendor.getRatingAverage().doubleValue() / 5.0 * 0.2;
        }
        
        // Experience score
        if (vendor.getYearsOfExperience() != null) {
            score += Math.min(vendor.getYearsOfExperience() / 10.0, 1.0) * 0.1;
        }
        
        // Verification boost
        if (vendor.getIsVerified()) {
            score += 0.1;
        }
        
        // Premium boost
        if (vendor.getIsPremium()) {
            score += 0.05;
        }
        
        // Feature preferences matching
        for (Map.Entry<String, Double> entry : featurePreferences.entrySet()) {
            String feature = entry.getKey();
            double preference = entry.getValue();
            
            if (vendorHasFeature(vendor, feature)) {
                score += preference * 0.25;
            }
        }
        
        return Math.min(score, 1.0);
    }
    
    /**
     * Calculates popularity score based on vendor metrics
     */
    private double calculatePopularityScore(Vendor vendor) {
        double score = 0.0;
        
        // Rating component
        if (vendor.getRatingAverage() != null && vendor.getTotalReviews() > 0) {
            double ratingScore = vendor.getRatingAverage().doubleValue() / 5.0;
            double reviewWeight = Math.min(vendor.getTotalReviews() / 100.0, 1.0);
            score += ratingScore * reviewWeight * 0.4;
        }
        
        // Booking component
        if (vendor.getTotalBookings() > 0) {
            double bookingScore = Math.min(vendor.getTotalBookings() / 50.0, 1.0);
            score += bookingScore * 0.3;
        }
        
        // Completion rate component
        double completionRate = vendor.getCompletionRate();
        score += completionRate / 100.0 * 0.2;
        
        // Recency boost
        if (vendor.getLastActive() != null) {
            long daysSinceActive = java.time.temporal.ChronoUnit.DAYS.between(vendor.getLastActive(), LocalDateTime.now());
            double recencyScore = Math.max(0, 1.0 - daysSinceActive / 30.0);
            score += recencyScore * 0.1;
        }
        
        return Math.min(score, 1.0);
    }
    
    /**
     * Calculates location-based score
     */
    private double calculateLocationScore(double distanceKm) {
        if (distanceKm <= 5) return 1.0;
        if (distanceKm <= 15) return 0.8;
        if (distanceKm <= 30) return 0.6;
        if (distanceKm <= 50) return 0.4;
        return 0.2;
    }
    
    /**
     * Calculates distance between two points using Haversine formula
     */
    private double calculateDistance(double lat1, double lon1, double lat2, double lon2) {
        final int R = 6371; // Radius of the earth in km
        
        double latDistance = Math.toRadians(lat2 - lat1);
        double lonDistance = Math.toRadians(lon2 - lon1);
        double a = Math.sin(latDistance / 2) * Math.sin(latDistance / 2)
                + Math.cos(Math.toRadians(lat1)) * Math.cos(Math.toRadians(lat2))
                * Math.sin(lonDistance / 2) * Math.sin(lonDistance / 2);
        double c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
        
        return R * c;
    }
    
    private void addWeightedScores(Map<String, Double> combinedScores, Map<String, Set<String>> vendorSources,
                                  List<RecommendationResult> results, double weight) {
        for (RecommendationResult result : results) {
            combinedScores.merge(result.getVendorId(), result.getScore() * weight, Double::sum);
            vendorSources.computeIfAbsent(result.getVendorId(), k -> new HashSet<>()).add(result.getSource());
        }
    }
    
    private double applyBusinessBoosts(RecommendationResult result, RecommendationContext context) {
        double score = result.getScore();
        
        Optional<Vendor> vendorOpt = vendorRepository.findById(result.getVendorId());
        if (vendorOpt.isPresent()) {
            Vendor vendor = vendorOpt.get();
            
            // Featured vendor boost
            if (vendor.getIsFeatured()) {
                score *= 1.2;
            }
            
            // Premium subscription boost
            if (vendor.getIsPremium()) {
                score *= 1.1;
            }
            
            // Fast response boost
            if (vendor.getResponseTimeHours() != null && vendor.getResponseTimeHours() <= 2) {
                score *= 1.05;
            }
        }
        
        return Math.min(score, 1.0);
    }
    
    private boolean isVendorAvailable(String vendorId, LocalDateTime eventDate) {
        // Implementation would check vendor availability calendar
        return true; // Placeholder
    }
    
    private boolean vendorHasFeature(Vendor vendor, String feature) {
        // Implementation would check if vendor offers specific feature
        return vendor.getSpecializations() != null && vendor.getSpecializations().contains(feature);
    }
    
    private List<String> getFallbackRecommendations(RecommendationContext context) {
        // Return popular vendors as fallback
        return vendorRepository.findTopRatedVendorsByCategory(context.getRequest().getCategory(), 10)
                .stream()
                .map(Vendor::getId)
                .collect(Collectors.toList());
    }
}
