package com.wedding.user.audit;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import jakarta.persistence.*;
import java.time.LocalDateTime;
import java.util.List;

/**
 * Audit log entity for comprehensive user activity tracking
 */
@Entity
@Table(name = "user_audit_logs", indexes = {
    @Index(name = "idx_audit_user_id", columnList = "user_id"),
    @Index(name = "idx_audit_email", columnList = "email"),
    @Index(name = "idx_audit_action", columnList = "action"),
    @Index(name = "idx_audit_timestamp", columnList = "timestamp"),
    @Index(name = "idx_audit_ip_address", columnList = "ip_address"),
    @Index(name = "idx_audit_performed_by", columnList = "performed_by")
})
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserAuditLog {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "user_id")
    private String userId;
    
    @Column(name = "email")
    private String email;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "action", nullable = false)
    private AuditAction action;
    
    @Column(name = "description", nullable = false)
    private String description;
    
    @Column(name = "details", columnDefinition = "TEXT")
    private String details;
    
    @Column(name = "before_snapshot", columnDefinition = "TEXT")
    private String beforeSnapshot;
    
    @Column(name = "after_snapshot", columnDefinition = "TEXT")
    private String afterSnapshot;
    
    @Column(name = "ip_address")
    private String ipAddress;
    
    @Column(name = "user_agent", columnDefinition = "TEXT")
    private String userAgent;
    
    @Column(name = "performed_by")
    private String performedBy;
    
    @Column(name = "timestamp", nullable = false)
    private LocalDateTime timestamp;
    
    @Column(name = "risk_score")
    private Double riskScore;
    
    @Column(name = "compliance_flags")
    private String complianceFlags;
    
    @Column(name = "retention_until")
    private LocalDateTime retentionUntil;
}

/**
 * Enumeration of all possible audit actions
 */
public enum AuditAction {
    // User lifecycle events
    USER_REGISTERED,
    USER_REGISTRATION_FAILED,
    USER_UPDATED,
    USER_DELETED,
    USER_LOCKED,
    USER_UNLOCKED,
    USER_SUSPENDED,
    USER_REACTIVATED,
    
    // Authentication events
    USER_LOGIN_SUCCESS,
    USER_LOGIN_FAILED,
    USER_LOGOUT,
    USER_SESSION_EXPIRED,
    USER_SESSION_TERMINATED,
    
    // Email and verification events
    EMAIL_VERIFIED,
    EMAIL_VERIFICATION_FAILED,
    EMAIL_VERIFICATION_RESENT,
    EMAIL_CHANGED,
    
    // Password events
    PASSWORD_CHANGED,
    PASSWORD_CHANGE_FAILED,
    PASSWORD_RESET_REQUESTED,
    PASSWORD_RESET_COMPLETED,
    PASSWORD_RESET_FAILED,
    
    // Role and permission events
    ROLE_ASSIGNED,
    ROLE_REMOVED,
    PERMISSION_GRANTED,
    PERMISSION_REVOKED,
    
    // Data access events
    DATA_ACCESSED,
    DATA_EXPORTED,
    DATA_IMPORTED,
    DATA_MODIFIED,
    
    // Security events
    SECURITY_VIOLATION,
    SUSPICIOUS_ACTIVITY,
    FRAUD_DETECTED,
    RATE_LIMIT_EXCEEDED,
    
    // GDPR and compliance events
    GDPR_EVENT,
    CONSENT_GIVEN,
    CONSENT_WITHDRAWN,
    DATA_RETENTION_POLICY_APPLIED,
    
    // Administrative events
    ADMIN_ACTION,
    SYSTEM_EVENT,
    MAINTENANCE_EVENT,
    
    // API and integration events
    API_ACCESS,
    WEBHOOK_TRIGGERED,
    EXTERNAL_INTEGRATION,
    
    // Profile and preferences
    PROFILE_UPDATED,
    PREFERENCES_CHANGED,
    AVATAR_UPDATED,
    
    // Two-factor authentication
    TWO_FA_ENABLED,
    TWO_FA_DISABLED,
    TWO_FA_VERIFIED,
    TWO_FA_FAILED
}

/**
 * User snapshot for audit trail
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserSnapshot {
    private String userId;
    private String email;
    private String firstName;
    private String lastName;
    private String phoneNumber;
    private LocalDateTime dateOfBirth;
    private String city;
    private String state;
    private String country;
    private Boolean isActive;
    private Boolean isVerified;
    private String profileImageUrl;
    private LocalDateTime lastLogin;
    private Integer loginCount;
    private Integer failedLoginAttempts;
    private LocalDateTime lockedUntil;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    private LocalDateTime snapshotTime;
}

/**
 * Search criteria for audit logs
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AuditSearchCriteria {
    private String userId;
    private String email;
    private List<AuditAction> actions;
    private LocalDateTime fromDate;
    private LocalDateTime toDate;
    private String ipAddress;
    private String performedBy;
    private String searchText;
    private Double minRiskScore;
    private Double maxRiskScore;
    private List<String> complianceFlags;
}

/**
 * Repository for user audit logs with advanced querying capabilities
 */
public interface UserAuditRepository extends JpaRepository<UserAuditLog, Long> {
    
    Page<UserAuditLog> findByUserIdOrderByTimestampDesc(String userId, Pageable pageable);
    
    Page<UserAuditLog> findByEmailOrderByTimestampDesc(String email, Pageable pageable);
    
    Page<UserAuditLog> findByActionOrderByTimestampDesc(AuditAction action, Pageable pageable);
    
    Page<UserAuditLog> findByTimestampBetweenOrderByTimestampDesc(
            LocalDateTime from, LocalDateTime to, Pageable pageable);
    
    Page<UserAuditLog> findByIpAddressOrderByTimestampDesc(String ipAddress, Pageable pageable);
    
    Page<UserAuditLog> findByPerformedByOrderByTimestampDesc(String performedBy, Pageable pageable);
    
    List<UserAuditLog> findByUserIdAndActionOrderByTimestampDesc(String userId, AuditAction action);
    
    List<UserAuditLog> findByUserIdAndTimestampBetweenOrderByTimestampDesc(
            String userId, LocalDateTime from, LocalDateTime to);
    
    long countByTimestampBetween(LocalDateTime from, LocalDateTime to);
    
    long countByActionAndTimestampBetween(AuditAction action, LocalDateTime from, LocalDateTime to);
    
    long countByUserIdAndTimestampBetween(String userId, LocalDateTime from, LocalDateTime to);
    
    @Query("SELECT COUNT(DISTINCT a.userId) FROM UserAuditLog a WHERE a.timestamp BETWEEN :from AND :to")
    long countDistinctUsersByTimestampBetween(@Param("from") LocalDateTime from, @Param("to") LocalDateTime to);
    
    @Query("SELECT COUNT(DISTINCT a.ipAddress) FROM UserAuditLog a WHERE a.timestamp BETWEEN :from AND :to")
    long countDistinctIpAddressesByTimestampBetween(@Param("from") LocalDateTime from, @Param("to") LocalDateTime to);
    
    @Query("SELECT a FROM UserAuditLog a WHERE a.riskScore >= :minScore ORDER BY a.riskScore DESC, a.timestamp DESC")
    Page<UserAuditLog> findHighRiskEvents(@Param("minScore") Double minScore, Pageable pageable);
    
    @Query("SELECT a FROM UserAuditLog a WHERE a.userId = :userId AND a.action IN :actions ORDER BY a.timestamp DESC")
    List<UserAuditLog> findByUserIdAndActions(@Param("userId") String userId, @Param("actions") List<AuditAction> actions);
    
    @Query("SELECT a FROM UserAuditLog a WHERE " +
           "(:userId IS NULL OR a.userId = :userId) AND " +
           "(:email IS NULL OR a.email = :email) AND " +
           "(:fromDate IS NULL OR a.timestamp >= :fromDate) AND " +
           "(:toDate IS NULL OR a.timestamp <= :toDate) AND " +
           "(:ipAddress IS NULL OR a.ipAddress = :ipAddress) AND " +
           "(:performedBy IS NULL OR a.performedBy = :performedBy) AND " +
           "(:minRiskScore IS NULL OR a.riskScore >= :minRiskScore) AND " +
           "(:maxRiskScore IS NULL OR a.riskScore <= :maxRiskScore) " +
           "ORDER BY a.timestamp DESC")
    Page<UserAuditLog> findByCriteria(
            @Param("userId") String userId,
            @Param("email") String email,
            @Param("fromDate") LocalDateTime fromDate,
            @Param("toDate") LocalDateTime toDate,
            @Param("ipAddress") String ipAddress,
            @Param("performedBy") String performedBy,
            @Param("minRiskScore") Double minRiskScore,
            @Param("maxRiskScore") Double maxRiskScore,
            Pageable pageable);
    
    default Page<UserAuditLog> findByCriteria(AuditSearchCriteria criteria, Pageable pageable) {
        return findByCriteria(
                criteria.getUserId(),
                criteria.getEmail(),
                criteria.getFromDate(),
                criteria.getToDate(),
                criteria.getIpAddress(),
                criteria.getPerformedBy(),
                criteria.getMinRiskScore(),
                criteria.getMaxRiskScore(),
                pageable
        );
    }
    
    @Query("SELECT a.action, COUNT(a) FROM UserAuditLog a WHERE a.timestamp BETWEEN :from AND :to GROUP BY a.action")
    List<Object[]> getActionStatistics(@Param("from") LocalDateTime from, @Param("to") LocalDateTime to);
    
    @Query("SELECT DATE(a.timestamp), COUNT(a) FROM UserAuditLog a WHERE a.timestamp BETWEEN :from AND :to GROUP BY DATE(a.timestamp) ORDER BY DATE(a.timestamp)")
    List<Object[]> getDailyActivityStatistics(@Param("from") LocalDateTime from, @Param("to") LocalDateTime to);
    
    @Query("SELECT a.ipAddress, COUNT(a) FROM UserAuditLog a WHERE a.timestamp BETWEEN :from AND :to GROUP BY a.ipAddress ORDER BY COUNT(a) DESC")
    List<Object[]> getTopIpAddresses(@Param("from") LocalDateTime from, @Param("to") LocalDateTime to, Pageable pageable);
    
    @Query("SELECT a FROM UserAuditLog a WHERE a.retentionUntil < :now")
    List<UserAuditLog> findExpiredAuditLogs(@Param("now") LocalDateTime now);
    
    @Query("SELECT a FROM UserAuditLog a WHERE a.complianceFlags LIKE %:flag%")
    Page<UserAuditLog> findByComplianceFlag(@Param("flag") String flag, Pageable pageable);
}
