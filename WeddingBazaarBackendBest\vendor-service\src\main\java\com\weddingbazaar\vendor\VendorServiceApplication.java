package com.wedding.vendor;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.netflix.eureka.EnableEurekaClient;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import org.springframework.data.mongodb.config.EnableMongoAuditing;
import org.springframework.kafka.annotation.EnableKafka;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * Vendor Service Application
 */
@SpringBootApplication(scanBasePackages = {"com.wedding.vendor", "com.wedding.shared"})
@EnableEurekaClient
@EnableJpaAuditing
@EnableMongoAuditing
@EnableKafka
@EnableAsync
@EnableScheduling
public class VendorServiceApplication {

    public static void main(String[] args) {
        SpringApplication.run(VendorServiceApplication.class, args);
    }
}
