package com.weddingbazaar.vendor;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.netflix.eureka.EnableEurekaClient;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import org.springframework.data.mongodb.config.EnableMongoAuditing;
import org.springframework.kafka.annotation.EnableKafka;

/**
 * Vendor Service Application
 */
@SpringBootApplication(scanBasePackages = {"com.weddingbazaar.vendor", "com.weddingbazaar.common"})
@EnableEurekaClient
@EnableJpaAuditing
@EnableMongoAuditing
@EnableKafka
public class VendorServiceApplication {
    
    public static void main(String[] args) {
        SpringApplication.run(VendorServiceApplication.class, args);
    }
}
