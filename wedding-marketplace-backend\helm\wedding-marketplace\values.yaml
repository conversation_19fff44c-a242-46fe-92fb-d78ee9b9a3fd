# Advanced Helm values for Wedding Marketplace Backend
# Global configuration
global:
  imageRegistry: ""
  imagePullSecrets: []
  storageClass: ""
  
# Application configuration
app:
  name: wedding-marketplace-backend
  version: "1.0.0"
  
# Image configuration
image:
  registry: docker.io
  repository: wedding-marketplace/backend
  tag: "1.0.0"
  pullPolicy: Always
  pullSecrets: []

# Deployment configuration
deployment:
  replicaCount: 3
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  
  # Pod configuration
  podAnnotations:
    prometheus.io/scrape: "true"
    prometheus.io/port: "9090"
    prometheus.io/path: "/actuator/prometheus"
  
  podLabels: {}
  
  # Security context
  securityContext:
    runAsNonRoot: true
    runAsUser: 1001
    runAsGroup: 1001
    fsGroup: 1001
  
  # Container configuration
  containerSecurityContext:
    allowPrivilegeEscalation: false
    readOnlyRootFilesystem: false
    capabilities:
      drop:
        - ALL

# Service configuration
service:
  type: LoadBalancer
  ports:
    http: 80
    https: 443
    grpc: 9091
  targetPorts:
    http: 8080
    https: 8080
    grpc: 9091
  annotations:
    service.beta.kubernetes.io/aws-load-balancer-type: "nlb"
    service.beta.kubernetes.io/aws-load-balancer-backend-protocol: "tcp"

# Internal service for cluster communication
internalService:
  enabled: true
  type: ClusterIP
  ports:
    http: 8080
    management: 9090
    grpc: 9091

# Ingress configuration
ingress:
  enabled: true
  className: "nginx"
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: /
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
  hosts:
    - host: api.wedding-marketplace.com
      paths:
        - path: /
          pathType: Prefix
  tls:
    - secretName: wedding-marketplace-tls
      hosts:
        - api.wedding-marketplace.com

# Resource configuration
resources:
  requests:
    memory: "512Mi"
    cpu: "250m"
  limits:
    memory: "2Gi"
    cpu: "1000m"

# Health checks
healthChecks:
  liveness:
    enabled: true
    path: /actuator/health/liveness
    port: management
    initialDelaySeconds: 60
    periodSeconds: 30
    timeoutSeconds: 10
    failureThreshold: 3
  
  readiness:
    enabled: true
    path: /actuator/health/readiness
    port: management
    initialDelaySeconds: 30
    periodSeconds: 10
    timeoutSeconds: 5
    failureThreshold: 3
  
  startup:
    enabled: true
    path: /actuator/health/startup
    port: management
    initialDelaySeconds: 10
    periodSeconds: 10
    timeoutSeconds: 5
    failureThreshold: 30

# Autoscaling configuration
autoscaling:
  enabled: true
  minReplicas: 3
  maxReplicas: 20
  targetCPUUtilizationPercentage: 70
  targetMemoryUtilizationPercentage: 80
  behavior:
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
        - type: Percent
          value: 10
          periodSeconds: 60
    scaleUp:
      stabilizationWindowSeconds: 60
      policies:
        - type: Percent
          value: 50
          periodSeconds: 60
        - type: Pods
          value: 2
          periodSeconds: 60

# Pod Disruption Budget
podDisruptionBudget:
  enabled: true
  minAvailable: 2

# Node affinity and tolerations
affinity:
  podAntiAffinity:
    preferredDuringSchedulingIgnoredDuringExecution:
      - weight: 100
        podAffinityTerm:
          labelSelector:
            matchExpressions:
              - key: app.kubernetes.io/name
                operator: In
                values:
                  - wedding-marketplace-backend
          topologyKey: kubernetes.io/hostname

tolerations:
  - key: "node.kubernetes.io/not-ready"
    operator: "Exists"
    effect: "NoExecute"
    tolerationSeconds: 300
  - key: "node.kubernetes.io/unreachable"
    operator: "Exists"
    effect: "NoExecute"
    tolerationSeconds: 300

# Environment variables
env:
  SPRING_PROFILES_ACTIVE: "kubernetes,prod"
  JAVA_OPTS: "-XX:+UseContainerSupport -XX:MaxRAMPercentage=75.0 -XX:+UseG1GC"

# Configuration
config:
  application.yml: |
    server:
      port: 8080
    management:
      server:
        port: 9090
      endpoints:
        web:
          exposure:
            include: health,info,metrics,prometheus
      endpoint:
        health:
          show-details: always
    spring:
      application:
        name: wedding-marketplace-backend
    logging:
      level:
        com.weddingmarketplace: INFO
      pattern:
        console: "%d{yyyy-MM-dd HH:mm:ss} - %msg%n"
        file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"

# Secrets (these should be provided via external secret management)
secrets:
  database:
    url: ""
    username: ""
    password: ""
  jwt:
    secret: ""
  oauth:
    clientId: ""
    clientSecret: ""

# Volume mounts
volumes:
  - name: config-volume
    configMap:
      name: wedding-marketplace-config
  - name: logs-volume
    emptyDir: {}
  - name: temp-volume
    emptyDir: {}

volumeMounts:
  - name: config-volume
    mountPath: /app/config
    readOnly: true
  - name: logs-volume
    mountPath: /app/logs
  - name: temp-volume
    mountPath: /tmp

# Service Account
serviceAccount:
  create: true
  annotations: {}
  name: ""

# RBAC
rbac:
  create: true
  rules:
    - apiGroups: [""]
      resources: ["configmaps", "secrets"]
      verbs: ["get", "list", "watch"]
    - apiGroups: [""]
      resources: ["pods"]
      verbs: ["get", "list", "watch"]

# Dependencies configuration
mysql:
  enabled: true
  auth:
    rootPassword: "rootpassword"
    database: "wedding_marketplace"
    username: "wm_user"
    password: "wm_password"
  primary:
    persistence:
      enabled: true
      size: 20Gi

redis:
  enabled: true
  auth:
    enabled: false
  master:
    persistence:
      enabled: true
      size: 8Gi

kafka:
  enabled: true
  persistence:
    enabled: true
    size: 10Gi
  zookeeper:
    persistence:
      enabled: true
      size: 8Gi

elasticsearch:
  enabled: true
  master:
    persistence:
      enabled: true
      size: 10Gi
  data:
    persistence:
      enabled: true
      size: 20Gi

# Monitoring configuration
monitoring:
  prometheus:
    enabled: true
    serviceMonitor:
      enabled: true
      interval: 30s
      path: /actuator/prometheus
      port: management
  
  grafana:
    enabled: true
    adminPassword: "admin"
    dashboards:
      enabled: true

# Tracing configuration
tracing:
  jaeger:
    enabled: true
    agent:
      enabled: true

# Network policies
networkPolicy:
  enabled: true
  ingress:
    - from:
        - namespaceSelector:
            matchLabels:
              name: ingress-nginx
      ports:
        - protocol: TCP
          port: 8080
    - from:
        - namespaceSelector:
            matchLabels:
              name: monitoring
      ports:
        - protocol: TCP
          port: 9090

# Backup configuration
backup:
  enabled: false
  schedule: "0 2 * * *"
  retention: "7d"
