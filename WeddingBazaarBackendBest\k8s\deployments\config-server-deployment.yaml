apiVersion: apps/v1
kind: Deployment
metadata:
  name: config-server
  namespace: wedding-bazaar
  labels:
    app: config-server
    version: v1
spec:
  replicas: 1
  selector:
    matchLabels:
      app: config-server
  template:
    metadata:
      labels:
        app: config-server
        version: v1
    spec:
      containers:
      - name: config-server
        image: wedding-bazaar/config-server:latest
        ports:
        - containerPort: 8888
          name: http
        env:
        - name: SPRING_PROFILES_ACTIVE
          value: "kubernetes"
        - name: EUREKA_CLIENT_SERVICE_URL_DEFAULTZONE
          value: "http://service-registry:8761/eureka/"
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /actuator/health
            port: 8888
          initialDelaySeconds: 60
          periodSeconds: 30
        readinessProbe:
          httpGet:
            path: /actuator/health
            port: 8888
          initialDelaySeconds: 30
          periodSeconds: 10
---
apiVersion: v1
kind: Service
metadata:
  name: config-server
  namespace: wedding-bazaar
  labels:
    app: config-server
spec:
  type: ClusterIP
  ports:
  - port: 8888
    targetPort: 8888
    name: http
  selector:
    app: config-server
