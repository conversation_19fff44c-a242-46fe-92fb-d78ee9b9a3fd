server:
  port: 8081

spring:
  application:
    name: user-service
  datasource:
    url: **********************************************
    username: ${DB_USERNAME:postgres}
    password: ${DB_PASSWORD:postgres}
  jpa:
    hibernate:
      ddl-auto: validate
    show-sql: false
  flyway:
    locations: classpath:db/migration
    baseline-version: 1
    baseline-description: Initial version
  security:
    oauth2:
      client:
        registration:
          google:
            client-id: ${oauth2.google.client-id}
            client-secret: ${oauth2.google.client-secret}
            scope: openid,profile,email
            redirect-uri: "{baseUrl}/oauth2/callback/{registrationId}"
            authorization-grant-type: authorization_code
          facebook:
            client-id: ${oauth2.facebook.client-id}
            client-secret: ${oauth2.facebook.client-secret}
            scope: email,public_profile
            redirect-uri: "{baseUrl}/oauth2/callback/{registrationId}"
            authorization-grant-type: authorization_code
        provider:
          google:
            authorization-uri: https://accounts.google.com/o/oauth2/v2/auth
            token-uri: https://oauth2.googleapis.com/token
            user-info-uri: https://www.googleapis.com/oauth2/v2/userinfo
            user-name-attribute: id
          facebook:
            authorization-uri: https://www.facebook.com/v3.0/dialog/oauth
            token-uri: https://graph.facebook.com/v3.0/oauth/access_token
            user-info-uri: https://graph.facebook.com/v3.0/me?fields=id,first_name,last_name,email,picture
            user-name-attribute: id

# User Service Specific Configuration
user-service:
  verification:
    token-expiry-hours: 24
    max-attempts: 3
  password-reset:
    token-expiry-hours: 1
    max-attempts: 5
  account-lockout:
    max-failed-attempts: 5
    lockout-duration-minutes: 30
  registration:
    auto-verify-oauth: true
    default-role: CUSTOMER
    welcome-email: true

logging:
  level:
    com.wedding.user: DEBUG
    org.springframework.security.oauth2: DEBUG
