package com.wedding.shared.exception;

import lombok.Getter;

import java.util.List;

/**
 * Custom validation exception with detailed error messages
 */
@Getter
public class ValidationException extends RuntimeException {
    
    private final List<String> errors;
    
    public ValidationException(String message) {
        super(message);
        this.errors = List.of(message);
    }
    
    public ValidationException(String message, List<String> errors) {
        super(message);
        this.errors = errors;
    }
    
    public ValidationException(List<String> errors) {
        super("Validation failed");
        this.errors = errors;
    }
}
