package com.weddingbazaar.user.controller;

import com.weddingbazaar.common.dto.BaseResponse;
import com.weddingbazaar.user.dto.AuthResponse;
import com.weddingbazaar.user.dto.UserLoginRequest;
import com.weddingbazaar.user.dto.UserRegistrationRequest;
import com.weddingbazaar.user.service.UserService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

/**
 * Authentication controller
 */
@RestController
@RequestMapping("/api/auth")
@RequiredArgsConstructor
@Slf4j
public class AuthController {
    
    private final UserService userService;
    
    @PostMapping("/register")
    public ResponseEntity<BaseResponse<AuthResponse>> register(
            @Valid @RequestBody UserRegistrationRequest request) {
        
        log.info("User registration request for email: {}", request.getEmail());
        AuthResponse response = userService.registerUser(request);
        return ResponseEntity.status(HttpStatus.CREATED)
                .body(BaseResponse.success(response, "User registered successfully"));
    }
    
    @PostMapping("/login")
    public ResponseEntity<BaseResponse<AuthResponse>> login(
            @Valid @RequestBody UserLoginRequest request) {
        
        log.info("User login request for email: {}", request.getEmail());
        AuthResponse response = userService.loginUser(request);
        return ResponseEntity.ok(BaseResponse.success(response, "Login successful"));
    }
}
