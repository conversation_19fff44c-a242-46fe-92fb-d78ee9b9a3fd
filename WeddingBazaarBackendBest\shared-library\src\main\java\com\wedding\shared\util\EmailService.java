package com.wedding.shared.util;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mail.SimpleMailMessage;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Service;

import jakarta.mail.MessagingException;
import jakarta.mail.internet.MimeMessage;
import java.util.concurrent.CompletableFuture;

/**
 * Email service for sending notifications and communications
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class EmailService {
    
    private final JavaMailSender mailSender;
    
    @Value("${app.mail.from:<EMAIL>}")
    private String fromEmail;
    
    @Value("${app.name:Wedding Bazaar}")
    private String appName;
    
    public CompletableFuture<Boolean> sendSimpleEmail(String to, String subject, String text) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                SimpleMailMessage message = new SimpleMailMessage();
                message.setFrom(fromEmail);
                message.setTo(to);
                message.setSubject(subject);
                message.setText(text);
                
                mailSender.send(message);
                log.info("Simple email sent successfully to: {}", to);
                return true;
            } catch (Exception e) {
                log.error("Failed to send simple email to: {}", to, e);
                return false;
            }
        });
    }
    
    public CompletableFuture<Boolean> sendHtmlEmail(String to, String subject, String htmlContent) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                MimeMessage message = mailSender.createMimeMessage();
                MimeMessageHelper helper = new MimeMessageHelper(message, true, "UTF-8");
                
                helper.setFrom(fromEmail, appName);
                helper.setTo(to);
                helper.setSubject(subject);
                helper.setText(htmlContent, true);
                
                mailSender.send(message);
                log.info("HTML email sent successfully to: {}", to);
                return true;
            } catch (MessagingException e) {
                log.error("Failed to send HTML email to: {}", to, e);
                return false;
            }
        });
    }
    
    public CompletableFuture<Boolean> sendWelcomeEmail(String to, String firstName) {
        String subject = "Welcome to " + appName + "!";
        String htmlContent = buildWelcomeEmailTemplate(firstName);
        return sendHtmlEmail(to, subject, htmlContent);
    }
    
    public CompletableFuture<Boolean> sendVerificationEmail(String to, String firstName, String verificationToken) {
        String subject = "Verify your " + appName + " account";
        String htmlContent = buildVerificationEmailTemplate(firstName, verificationToken);
        return sendHtmlEmail(to, subject, htmlContent);
    }
    
    public CompletableFuture<Boolean> sendPasswordResetEmail(String to, String firstName, String resetToken) {
        String subject = "Reset your " + appName + " password";
        String htmlContent = buildPasswordResetEmailTemplate(firstName, resetToken);
        return sendHtmlEmail(to, subject, htmlContent);
    }
    
    public CompletableFuture<Boolean> sendBookingConfirmationEmail(String to, String firstName, String bookingId) {
        String subject = "Booking Confirmation - " + bookingId;
        String htmlContent = buildBookingConfirmationTemplate(firstName, bookingId);
        return sendHtmlEmail(to, subject, htmlContent);
    }
    
    private String buildWelcomeEmailTemplate(String firstName) {
        return """
            <html>
            <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
                <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                    <h2 style="color: #e91e63;">Welcome to %s!</h2>
                    <p>Dear %s,</p>
                    <p>Thank you for joining our wedding marketplace platform. We're excited to help you plan your perfect wedding!</p>
                    <p>You can now:</p>
                    <ul>
                        <li>Browse and connect with verified vendors</li>
                        <li>Book services for your special day</li>
                        <li>Manage your wedding planning in one place</li>
                    </ul>
                    <p>If you have any questions, feel free to contact our support team.</p>
                    <p>Best regards,<br>The %s Team</p>
                </div>
            </body>
            </html>
            """.formatted(appName, firstName, appName);
    }
    
    private String buildVerificationEmailTemplate(String firstName, String verificationToken) {
        String verificationUrl = "https://weddingbazaar.com/verify?token=" + verificationToken;
        
        return """
            <html>
            <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
                <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                    <h2 style="color: #e91e63;">Verify Your Account</h2>
                    <p>Dear %s,</p>
                    <p>Please click the button below to verify your email address and activate your account:</p>
                    <div style="text-align: center; margin: 30px 0;">
                        <a href="%s" style="background-color: #e91e63; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;">Verify Account</a>
                    </div>
                    <p>If the button doesn't work, copy and paste this link into your browser:</p>
                    <p><a href="%s">%s</a></p>
                    <p>This link will expire in 24 hours.</p>
                    <p>Best regards,<br>The %s Team</p>
                </div>
            </body>
            </html>
            """.formatted(firstName, verificationUrl, verificationUrl, verificationUrl, appName);
    }
    
    private String buildPasswordResetEmailTemplate(String firstName, String resetToken) {
        String resetUrl = "https://weddingbazaar.com/reset-password?token=" + resetToken;
        
        return """
            <html>
            <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
                <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                    <h2 style="color: #e91e63;">Reset Your Password</h2>
                    <p>Dear %s,</p>
                    <p>You requested to reset your password. Click the button below to set a new password:</p>
                    <div style="text-align: center; margin: 30px 0;">
                        <a href="%s" style="background-color: #e91e63; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;">Reset Password</a>
                    </div>
                    <p>If you didn't request this, please ignore this email.</p>
                    <p>This link will expire in 1 hour.</p>
                    <p>Best regards,<br>The %s Team</p>
                </div>
            </body>
            </html>
            """.formatted(firstName, resetUrl, appName);
    }
    
    private String buildBookingConfirmationTemplate(String firstName, String bookingId) {
        return """
            <html>
            <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
                <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                    <h2 style="color: #e91e63;">Booking Confirmed!</h2>
                    <p>Dear %s,</p>
                    <p>Your booking has been confirmed. Here are the details:</p>
                    <div style="background-color: #f5f5f5; padding: 15px; border-radius: 5px; margin: 20px 0;">
                        <p><strong>Booking ID:</strong> %s</p>
                        <p><strong>Status:</strong> Confirmed</p>
                    </div>
                    <p>You will receive further updates about your booking via email.</p>
                    <p>Thank you for choosing %s!</p>
                    <p>Best regards,<br>The %s Team</p>
                </div>
            </body>
            </html>
            """.formatted(firstName, bookingId, appName, appName);
    }
}
