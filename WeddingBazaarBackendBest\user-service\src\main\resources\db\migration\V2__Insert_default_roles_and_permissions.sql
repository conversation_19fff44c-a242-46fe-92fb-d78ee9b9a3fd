-- V2__Insert_default_roles_and_permissions.sql
-- Insert default roles and permissions

-- Insert default permissions
INSERT INTO permissions (name, description, resource, action) VALUES
-- User permissions
('USER_READ', 'Read user information', 'USER', 'READ'),
('USER_UPDATE', 'Update user information', 'USER', 'UPDATE'),
('USER_DELETE', 'Delete user account', 'USER', 'DELETE'),

-- Vendor permissions
('VENDOR_CREATE', 'Create vendor profile', 'VENDOR', 'CREATE'),
('VENDOR_READ', 'Read vendor information', 'VENDOR', 'READ'),
('VENDOR_UPDATE', 'Update vendor information', 'VENDOR', 'UPDATE'),
('VENDOR_DELETE', 'Delete vendor profile', 'VENDOR', 'DELETE'),

-- Booking permissions
('BOOKING_CREATE', 'Create bookings', 'BOOKING', 'CREATE'),
('BOOKING_READ', 'Read booking information', 'BOOKING', 'READ'),
('BOOKING_UPDATE', 'Update booking information', 'BOOKING', 'UPDATE'),
('BOOKING_CANCEL', 'Cancel bookings', 'BOOKING', 'CANCEL'),

-- Payment permissions
('PAYMENT_CREATE', 'Process payments', 'PAYMENT', 'CREATE'),
('PAYMENT_READ', 'Read payment information', 'PAYMENT', 'READ'),
('PAYMENT_REFUND', 'Process refunds', 'PAYMENT', 'REFUND'),

-- Admin permissions
('ADMIN_USER_MANAGE', 'Manage all users', 'ADMIN', 'USER_MANAGE'),
('ADMIN_VENDOR_MANAGE', 'Manage all vendors', 'ADMIN', 'VENDOR_MANAGE'),
('ADMIN_BOOKING_MANAGE', 'Manage all bookings', 'ADMIN', 'BOOKING_MANAGE'),
('ADMIN_PAYMENT_MANAGE', 'Manage all payments', 'ADMIN', 'PAYMENT_MANAGE'),
('ADMIN_SYSTEM_CONFIG', 'Configure system settings', 'ADMIN', 'SYSTEM_CONFIG'),
('ADMIN_ANALYTICS', 'Access analytics and reports', 'ADMIN', 'ANALYTICS');

-- Insert default roles
INSERT INTO roles (name, description) VALUES
('ROLE_CUSTOMER', 'Regular customer who can book services'),
('ROLE_VENDOR', 'Service provider who offers wedding services'),
('ROLE_ADMIN', 'Administrator with full system access'),
('ROLE_SUPER_ADMIN', 'Super administrator with all privileges');

-- Assign permissions to roles

-- Customer role permissions
INSERT INTO role_permissions (role_id, permission_id)
SELECT r.id, p.id FROM roles r, permissions p 
WHERE r.name = 'ROLE_CUSTOMER' AND p.name IN (
    'USER_READ', 'USER_UPDATE',
    'VENDOR_READ',
    'BOOKING_CREATE', 'BOOKING_READ', 'BOOKING_UPDATE', 'BOOKING_CANCEL',
    'PAYMENT_CREATE', 'PAYMENT_READ'
);

-- Vendor role permissions
INSERT INTO role_permissions (role_id, permission_id)
SELECT r.id, p.id FROM roles r, permissions p 
WHERE r.name = 'ROLE_VENDOR' AND p.name IN (
    'USER_READ', 'USER_UPDATE',
    'VENDOR_CREATE', 'VENDOR_READ', 'VENDOR_UPDATE',
    'BOOKING_READ', 'BOOKING_UPDATE',
    'PAYMENT_READ'
);

-- Admin role permissions
INSERT INTO role_permissions (role_id, permission_id)
SELECT r.id, p.id FROM roles r, permissions p 
WHERE r.name = 'ROLE_ADMIN' AND p.name IN (
    'USER_READ', 'USER_UPDATE', 'USER_DELETE',
    'VENDOR_READ', 'VENDOR_UPDATE', 'VENDOR_DELETE',
    'BOOKING_READ', 'BOOKING_UPDATE', 'BOOKING_CANCEL',
    'PAYMENT_READ', 'PAYMENT_REFUND',
    'ADMIN_USER_MANAGE', 'ADMIN_VENDOR_MANAGE', 'ADMIN_BOOKING_MANAGE', 
    'ADMIN_PAYMENT_MANAGE', 'ADMIN_ANALYTICS'
);

-- Super Admin role permissions (all permissions)
INSERT INTO role_permissions (role_id, permission_id)
SELECT r.id, p.id FROM roles r, permissions p 
WHERE r.name = 'ROLE_SUPER_ADMIN';

-- Create default super admin user (password: admin123)
-- Password hash for 'admin123' using BCrypt with strength 12
INSERT INTO users (
    id, email, password, first_name, last_name, 
    is_active, is_verified, created_at, updated_at
) VALUES (
    gen_random_uuid(),
    '<EMAIL>',
    '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewfBPdkKBs.a6Mce', -- admin123
    'Super',
    'Admin',
    true,
    true,
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
);

-- Assign super admin role to the default admin user
INSERT INTO user_roles (user_id, role_id)
SELECT u.id, r.id FROM users u, roles r 
WHERE u.email = '<EMAIL>' AND r.name = 'ROLE_SUPER_ADMIN';
