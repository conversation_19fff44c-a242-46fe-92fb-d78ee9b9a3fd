# Multi-stage build for API Gateway
FROM openjdk:17-jdk-slim as builder

WORKDIR /app

COPY .mvn/ .mvn/
COPY mvnw pom.xml ./
COPY shared-library/pom.xml shared-library/
COPY api-gateway/pom.xml api-gateway/

RUN ./mvnw dependency:go-offline -B

COPY shared-library/src shared-library/src/
COPY api-gateway/src api-gateway/src/

RUN ./mvnw clean package -DskipTests -pl api-gateway -am

# Runtime stage
FROM openjdk:17-jre-slim

RUN apt-get update && apt-get install -y curl && rm -rf /var/lib/apt/lists/*
RUN groupadd -r appuser && useradd -r -g appuser appuser

WORKDIR /app

COPY --from=builder /app/api-gateway/target/api-gateway-*.jar app.jar
RUN chown -R appuser:appuser /app

USER appuser
EXPOSE 8080

HEALTHCHECK --interval=30s --timeout=3s --start-period=60s --retries=3 \
  CMD curl -f http://localhost:8080/actuator/health || exit 1

ENTRYPOINT ["java", "-XX:+UseContainerSupport", "-XX:MaxRAMPercentage=75.0", "-jar", "app.jar"]
