package com.weddingbazaar.user.service;

import com.weddingbazaar.common.dto.PageResponse;
import com.weddingbazaar.common.enums.UserRole;
import com.weddingbazaar.common.exception.BusinessException;
import com.weddingbazaar.common.exception.ResourceNotFoundException;
import com.weddingbazaar.common.security.JwtUtil;
import com.weddingbazaar.user.dto.*;
import com.weddingbazaar.user.entity.User;
import com.weddingbazaar.user.repository.UserRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * User service implementation
 */
@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class UserService {
    
    private final UserRepository userRepository;
    private final PasswordEncoder passwordEncoder;
    private final JwtUtil jwtUtil;
    private final KafkaTemplate<String, Object> kafkaTemplate;
    
    public AuthResponse registerUser(UserRegistrationRequest request) {
        log.info("Registering new user with email: {}", request.getEmail());
        
        if (userRepository.existsByEmail(request.getEmail())) {
            throw new BusinessException("User with this email already exists", "USER_ALREADY_EXISTS");
        }
        
        User user = User.builder()
                .email(request.getEmail())
                .password(passwordEncoder.encode(request.getPassword()))
                .firstName(request.getFirstName())
                .lastName(request.getLastName())
                .phoneNumber(request.getPhoneNumber())
                .role(request.getRole())
                .city(request.getCity())
                .state(request.getState())
                .country(request.getCountry())
                .isActive(true)
                .isVerified(false)
                .build();
        
        User savedUser = userRepository.save(user);
        
        // Publish user registration event
        publishUserEvent("user.registered", savedUser);
        
        // Generate tokens
        String accessToken = jwtUtil.generateToken(savedUser.getId(), savedUser.getEmail(), savedUser.getRole());
        String refreshToken = jwtUtil.generateRefreshToken(savedUser.getId());
        
        return AuthResponse.builder()
                .accessToken(accessToken)
                .refreshToken(refreshToken)
                .expiresIn(86400L) // 24 hours
                .user(toUserResponse(savedUser))
                .build();
    }
    
    public AuthResponse loginUser(UserLoginRequest request) {
        log.info("User login attempt for email: {}", request.getEmail());
        
        User user = userRepository.findByEmailAndIsActiveTrue(request.getEmail())
                .orElseThrow(() -> new BusinessException("Invalid credentials", "INVALID_CREDENTIALS"));
        
        if (!passwordEncoder.matches(request.getPassword(), user.getPassword())) {
            throw new BusinessException("Invalid credentials", "INVALID_CREDENTIALS");
        }
        
        // Update last login
        user.setLastLogin(LocalDateTime.now());
        userRepository.save(user);
        
        // Publish login event
        publishUserEvent("user.logged_in", user);
        
        // Generate tokens
        String accessToken = jwtUtil.generateToken(user.getId(), user.getEmail(), user.getRole());
        String refreshToken = jwtUtil.generateRefreshToken(user.getId());
        
        return AuthResponse.builder()
                .accessToken(accessToken)
                .refreshToken(refreshToken)
                .expiresIn(86400L) // 24 hours
                .user(toUserResponse(user))
                .build();
    }
    
    @Transactional(readOnly = true)
    public UserResponse getUserById(String userId) {
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new ResourceNotFoundException("User", userId));
        return toUserResponse(user);
    }
    
    @Transactional(readOnly = true)
    public UserResponse getUserByEmail(String email) {
        User user = userRepository.findByEmail(email)
                .orElseThrow(() -> new ResourceNotFoundException("User", email));
        return toUserResponse(user);
    }

    @Transactional(readOnly = true)
    public PageResponse<UserResponse> getAllUsers(Pageable pageable) {
        Page<User> userPage = userRepository.findAll(pageable);
        List<UserResponse> users = userPage.getContent().stream()
                .map(this::toUserResponse)
                .toList();

        return PageResponse.of(users, userPage.getNumber(), userPage.getSize(), userPage.getTotalElements());
    }

    @Transactional(readOnly = true)
    public PageResponse<UserResponse> getUsersByRole(UserRole role, Pageable pageable) {
        Page<User> userPage = userRepository.findByRole(role, pageable);
        List<UserResponse> users = userPage.getContent().stream()
                .map(this::toUserResponse)
                .toList();
        
        return PageResponse.of(users, userPage.getNumber(), userPage.getSize(), userPage.getTotalElements());
    }
    
    public UserResponse updateUser(String userId, UserUpdateRequest request) {
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new ResourceNotFoundException("User", userId));
        
        // Update fields
        if (request.getFirstName() != null) {
            user.setFirstName(request.getFirstName());
        }
        if (request.getLastName() != null) {
            user.setLastName(request.getLastName());
        }
        if (request.getPhoneNumber() != null) {
            user.setPhoneNumber(request.getPhoneNumber());
        }
        if (request.getCity() != null) {
            user.setCity(request.getCity());
        }
        if (request.getState() != null) {
            user.setState(request.getState());
        }
        if (request.getCountry() != null) {
            user.setCountry(request.getCountry());
        }
        if (request.getProfileImageUrl() != null) {
            user.setProfileImageUrl(request.getProfileImageUrl());
        }
        if (request.getDateOfBirth() != null) {
            user.setDateOfBirth(request.getDateOfBirth());
        }
        
        User updatedUser = userRepository.save(user);
        
        // Publish user update event
        publishUserEvent("user.updated", updatedUser);
        
        return toUserResponse(updatedUser);
    }

    public void deactivateUser(String userId) {
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new ResourceNotFoundException("User", userId));
        
        user.setIsActive(false);
        userRepository.save(user);
        
        // Publish user deactivation event
        publishUserEvent("user.deactivated", user);
        
        log.info("User deactivated: {}", userId);
    }
    
    public void verifyUser(String userId) {
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new ResourceNotFoundException("User", userId));
        
        user.setIsVerified(true);
        userRepository.save(user);
        
        // Publish user verification event
        publishUserEvent("user.verified", user);
        
        log.info("User verified: {}", userId);
    }
    
    private void publishUserEvent(String eventType, User user) {
        try {
            UserEvent event = UserEvent.builder()
                    .eventType(eventType)
                    .userId(user.getId())
                    .email(user.getEmail())
                    .role(user.getRole())
                    .timestamp(LocalDateTime.now())
                    .build();
            
            kafkaTemplate.send("user-events", event);
            log.debug("Published user event: {} for user: {}", eventType, user.getId());
        } catch (Exception e) {
            log.error("Failed to publish user event: {} for user: {}", eventType, user.getId(), e);
        }
    }

    private UserResponse toUserResponse(User user) {
        return UserResponse.builder()
                .id(user.getId())
                .email(user.getEmail())
                .firstName(user.getFirstName())
                .lastName(user.getLastName())
                .fullName(user.getFullName())
                .phoneNumber(user.getPhoneNumber())
                .role(user.getRole())
                .isActive(user.getIsActive())
                .isVerified(user.getIsVerified())
                .profileImageUrl(user.getProfileImageUrl())
                .dateOfBirth(user.getDateOfBirth())
                .city(user.getCity())
                .state(user.getState())
                .country(user.getCountry())
                .lastLogin(user.getLastLogin())
                .createdAt(user.getCreatedAt())
                .updatedAt(user.getUpdatedAt())
                .build();
    }
}
