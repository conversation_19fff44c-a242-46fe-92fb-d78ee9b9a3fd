package com.weddingbazaar.payment.entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * Payment entity
 */
@Entity
@Table(name = "payments")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EntityListeners(AuditingEntityListener.class)
public class Payment {
    
    @Id
    @GeneratedValue(strategy = GenerationType.UUID)
    private String id;
    
    @Column(name = "booking_id", nullable = false)
    private String bookingId;
    
    @Column(name = "user_id", nullable = false)
    private String userId;
    
    @Column(name = "vendor_id", nullable = false)
    private String vendorId;
    
    @Column(nullable = false)
    private BigDecimal amount;
    
    @Column(nullable = false)
    private String currency = "INR";
    
    @Enumerated(EnumType.STRING)
    @Column(name = "payment_status", nullable = false)
    private PaymentStatus status = PaymentStatus.PENDING;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "payment_method")
    private PaymentMethod paymentMethod;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "payment_provider")
    private PaymentProvider provider;
    
    @Column(name = "provider_payment_id")
    private String providerPaymentId; // Stripe payment intent ID, Razorpay payment ID
    
    @Column(name = "provider_session_id")
    private String providerSessionId; // Stripe session ID
    
    @Column(name = "transaction_id")
    private String transactionId;
    
    @Column(name = "payment_description")
    private String description;
    
    @Column(name = "commission_amount")
    private BigDecimal commissionAmount;
    
    @Column(name = "vendor_amount")
    private BigDecimal vendorAmount;
    
    @Column(name = "platform_fee")
    private BigDecimal platformFee;
    
    @Column(name = "tax_amount")
    private BigDecimal taxAmount;
    
    @Column(name = "refund_amount")
    private BigDecimal refundAmount = BigDecimal.ZERO;
    
    @Column(name = "refund_reason")
    private String refundReason;
    
    @Column(name = "failure_reason")
    private String failureReason;
    
    @Column(name = "payment_date")
    private LocalDateTime paymentDate;
    
    @Column(name = "refund_date")
    private LocalDateTime refundDate;
    
    @Column(name = "webhook_received")
    private Boolean webhookReceived = false;
    
    @Column(name = "webhook_data", columnDefinition = "TEXT")
    private String webhookData;
    
    @CreatedDate
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;
    
    @LastModifiedDate
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
    
    public enum PaymentStatus {
        PENDING,
        PROCESSING,
        COMPLETED,
        FAILED,
        CANCELLED,
        REFUNDED,
        PARTIALLY_REFUNDED
    }
    
    public enum PaymentMethod {
        CREDIT_CARD,
        DEBIT_CARD,
        UPI,
        NET_BANKING,
        WALLET,
        EMI,
        BANK_TRANSFER
    }
    
    public enum PaymentProvider {
        STRIPE,
        RAZORPAY,
        PAYU,
        PAYPAL
    }
}
