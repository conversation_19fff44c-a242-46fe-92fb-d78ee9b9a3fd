global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  # - "first_rules.yml"
  # - "second_rules.yml"

scrape_configs:
  # Prometheus itself
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  # Eureka Server
  - job_name: 'eureka-server'
    metrics_path: '/actuator/prometheus'
    static_configs:
      - targets: ['eureka-server:8761']

  # Config Server
  - job_name: 'config-server'
    metrics_path: '/actuator/prometheus'
    static_configs:
      - targets: ['config-server:8888']

  # API Gateway
  - job_name: 'api-gateway'
    metrics_path: '/actuator/prometheus'
    static_configs:
      - targets: ['api-gateway:8080']

  # User Service
  - job_name: 'user-service'
    metrics_path: '/actuator/prometheus'
    static_configs:
      - targets: ['user-service:8081']

  # Vendor Service
  - job_name: 'vendor-service'
    metrics_path: '/actuator/prometheus'
    static_configs:
      - targets: ['vendor-service:8082']

  # Booking Service
  - job_name: 'booking-service'
    metrics_path: '/actuator/prometheus'
    static_configs:
      - targets: ['booking-service:8083']

  # Search Service
  - job_name: 'search-service'
    metrics_path: '/actuator/prometheus'
    static_configs:
      - targets: ['search-service:8084']

  # Payment Service
  - job_name: 'payment-service'
    metrics_path: '/actuator/prometheus'
    static_configs:
      - targets: ['payment-service:8085']

  # Messaging Service
  - job_name: 'messaging-service'
    metrics_path: '/actuator/prometheus'
    static_configs:
      - targets: ['messaging-service:8086']

  # Analytics Service
  - job_name: 'analytics-service'
    metrics_path: '/actuator/prometheus'
    static_configs:
      - targets: ['analytics-service:8087']

  # Admin Service
  - job_name: 'admin-service'
    metrics_path: '/actuator/prometheus'
    static_configs:
      - targets: ['admin-service:8088']

  # Notification Service
  - job_name: 'notification-service'
    metrics_path: '/actuator/prometheus'
    static_configs:
      - targets: ['notification-service:8089']

  # Infrastructure monitoring
  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres:5432']

  - job_name: 'redis'
    static_configs:
      - targets: ['redis:6379']

  - job_name: 'elasticsearch'
    static_configs:
      - targets: ['elasticsearch:9200']

  - job_name: 'kafka'
    static_configs:
      - targets: ['kafka:9092']
