package com.weddingbazaar.search;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.netflix.eureka.EnableEurekaClient;
import org.springframework.kafka.annotation.EnableKafka;

/**
 * Search Service Application
 */
@SpringBootApplication(scanBasePackages = {"com.weddingbazaar.search", "com.weddingbazaar.common"})
@EnableEurekaClient
@EnableKafka
public class SearchServiceApplication {
    
    public static void main(String[] args) {
        SpringApplication.run(SearchServiceApplication.class, args);
    }
}
