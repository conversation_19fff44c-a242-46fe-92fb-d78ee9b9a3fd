package com.wedding.user.service;

import com.wedding.shared.exception.BusinessException;
import com.wedding.user.model.Role;
import com.wedding.user.model.User;
import com.wedding.user.repository.RoleRepository;
import com.wedding.user.repository.UserRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.oauth2.client.userinfo.DefaultOAuth2UserService;
import org.springframework.security.oauth2.client.userinfo.OAuth2UserRequest;
import org.springframework.security.oauth2.core.OAuth2AuthenticationException;
import org.springframework.security.oauth2.core.user.OAuth2User;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Map;
import java.util.Optional;
import java.util.Set;

/**
 * Custom OAuth2 user service for handling social login
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class CustomOAuth2UserService extends DefaultOAuth2UserService {
    
    private final UserRepository userRepository;
    private final RoleRepository roleRepository;
    
    @Override
    @Transactional
    public OAuth2User loadUser(OAuth2UserRequest userRequest) throws OAuth2AuthenticationException {
        OAuth2User oAuth2User = super.loadUser(userRequest);
        
        try {
            return processOAuth2User(userRequest, oAuth2User);
        } catch (Exception ex) {
            log.error("Error processing OAuth2 user", ex);
            throw new OAuth2AuthenticationException("Error processing OAuth2 user");
        }
    }
    
    private OAuth2User processOAuth2User(OAuth2UserRequest userRequest, OAuth2User oAuth2User) {
        String registrationId = userRequest.getClientRegistration().getRegistrationId();
        OAuth2UserInfo userInfo = OAuth2UserInfoFactory.getOAuth2UserInfo(registrationId, oAuth2User.getAttributes());
        
        if (userInfo.getEmail() == null || userInfo.getEmail().isEmpty()) {
            throw new BusinessException("Email not found from OAuth2 provider");
        }
        
        Optional<User> userOptional = userRepository.findByEmail(userInfo.getEmail());
        User user;
        
        if (userOptional.isPresent()) {
            user = userOptional.get();
            if (!user.getProvider().equals(registrationId)) {
                throw new BusinessException("Looks like you're signed up with " + user.getProvider() + " account. Please use your " + user.getProvider() + " account to login.");
            }
            user = updateExistingUser(user, userInfo);
        } else {
            user = registerNewUser(userRequest, userInfo);
        }
        
        return new CustomOAuth2User(oAuth2User.getAttributes(), user);
    }
    
    private User registerNewUser(OAuth2UserRequest userRequest, OAuth2UserInfo userInfo) {
        Role customerRole = roleRepository.findByName(Role.RoleName.ROLE_CUSTOMER)
                .orElseThrow(() -> new BusinessException("Customer role not found"));
        
        User user = User.builder()
                .email(userInfo.getEmail())
                .firstName(userInfo.getFirstName())
                .lastName(userInfo.getLastName())
                .profileImageUrl(userInfo.getImageUrl())
                .provider(userRequest.getClientRegistration().getRegistrationId())
                .providerId(userInfo.getId())
                .isVerified(true) // OAuth2 users are considered verified
                .isActive(true)
                .roles(Set.of(customerRole))
                .build();
        
        return userRepository.save(user);
    }
    
    private User updateExistingUser(User existingUser, OAuth2UserInfo userInfo) {
        existingUser.setFirstName(userInfo.getFirstName());
        existingUser.setLastName(userInfo.getLastName());
        existingUser.setProfileImageUrl(userInfo.getImageUrl());
        
        return userRepository.save(existingUser);
    }
    
    // OAuth2UserInfo interface and implementations
    public interface OAuth2UserInfo {
        String getId();
        String getEmail();
        String getFirstName();
        String getLastName();
        String getImageUrl();
    }
    
    public static class OAuth2UserInfoFactory {
        public static OAuth2UserInfo getOAuth2UserInfo(String registrationId, Map<String, Object> attributes) {
            if ("google".equals(registrationId)) {
                return new GoogleOAuth2UserInfo(attributes);
            } else if ("facebook".equals(registrationId)) {
                return new FacebookOAuth2UserInfo(attributes);
            } else {
                throw new BusinessException("Sorry! Login with " + registrationId + " is not supported yet.");
            }
        }
    }
    
    public static class GoogleOAuth2UserInfo implements OAuth2UserInfo {
        private final Map<String, Object> attributes;
        
        public GoogleOAuth2UserInfo(Map<String, Object> attributes) {
            this.attributes = attributes;
        }
        
        @Override
        public String getId() {
            return (String) attributes.get("sub");
        }
        
        @Override
        public String getEmail() {
            return (String) attributes.get("email");
        }
        
        @Override
        public String getFirstName() {
            return (String) attributes.get("given_name");
        }
        
        @Override
        public String getLastName() {
            return (String) attributes.get("family_name");
        }
        
        @Override
        public String getImageUrl() {
            return (String) attributes.get("picture");
        }
    }
    
    public static class FacebookOAuth2UserInfo implements OAuth2UserInfo {
        private final Map<String, Object> attributes;
        
        public FacebookOAuth2UserInfo(Map<String, Object> attributes) {
            this.attributes = attributes;
        }
        
        @Override
        public String getId() {
            return (String) attributes.get("id");
        }
        
        @Override
        public String getEmail() {
            return (String) attributes.get("email");
        }
        
        @Override
        public String getFirstName() {
            return (String) attributes.get("first_name");
        }
        
        @Override
        public String getLastName() {
            return (String) attributes.get("last_name");
        }
        
        @Override
        @SuppressWarnings("unchecked")
        public String getImageUrl() {
            if (attributes.containsKey("picture")) {
                Map<String, Object> pictureObj = (Map<String, Object>) attributes.get("picture");
                if (pictureObj.containsKey("data")) {
                    Map<String, Object> dataObj = (Map<String, Object>) pictureObj.get("data");
                    if (dataObj.containsKey("url")) {
                        return (String) dataObj.get("url");
                    }
                }
            }
            return null;
        }
    }
}
