package com.wedding.user.service.strategy;

import com.wedding.shared.exception.BusinessException;
import com.wedding.user.dto.UserRegistrationRequest;
import com.wedding.user.service.external.FraudDetectionService;
import com.wedding.user.service.external.GeolocationService;
import com.wedding.user.service.external.PhoneValidationService;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.regex.Pattern;

/**
 * Strategy pattern for user registration with multiple validation and enrichment strategies
 */
public interface UserRegistrationStrategy {
    
    void apply(UserRegistrationContext context);
    
    int getOrder();
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class UserRegistrationContext {
        private UserRegistrationRequest request;
        private Map<String, Object> enrichmentData;
        private String clientIpAddress;
        private String userAgent;
        private LocalDateTime registrationTime;
        
        public UserRegistrationContext(UserRegistrationRequest request) {
            this.request = request;
            this.enrichmentData = new java.util.HashMap<>();
            this.registrationTime = LocalDateTime.now();
        }
        
        public void addEnrichmentData(String key, Object value) {
            if (enrichmentData == null) {
                enrichmentData = new java.util.HashMap<>();
            }
            enrichmentData.put(key, value);
        }
    }
}

/**
 * Validates email format and domain
 */
@Component
@org.springframework.core.annotation.Order(1)
@Slf4j
class EmailValidationStrategy implements UserRegistrationStrategy {
    
    private static final Pattern EMAIL_PATTERN = Pattern.compile(
        "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$"
    );
    
    private static final java.util.Set<String> BLOCKED_DOMAINS = java.util.Set.of(
        "tempmail.org", "10minutemail.com", "guerrillamail.com", "mailinator.com"
    );
    
    @Override
    public void apply(UserRegistrationContext context) {
        String email = context.getRequest().getEmail();
        
        if (!EMAIL_PATTERN.matcher(email).matches()) {
            throw new BusinessException("Invalid email format", "INVALID_EMAIL_FORMAT");
        }
        
        String domain = email.substring(email.indexOf('@') + 1).toLowerCase();
        if (BLOCKED_DOMAINS.contains(domain)) {
            throw new BusinessException("Temporary email addresses are not allowed", "TEMP_EMAIL_BLOCKED");
        }
        
        // Check for suspicious patterns
        if (email.contains("..") || email.startsWith(".") || email.endsWith(".")) {
            throw new BusinessException("Invalid email format", "INVALID_EMAIL_FORMAT");
        }
        
        context.addEnrichmentData("emailDomain", domain);
        log.debug("Email validation passed for: {}", email);
    }
    
    @Override
    public int getOrder() {
        return 1;
    }
}

/**
 * Validates phone number format and authenticity
 */
@Component
@org.springframework.core.annotation.Order(2)
@RequiredArgsConstructor
@Slf4j
class PhoneValidationStrategy implements UserRegistrationStrategy {
    
    private final PhoneValidationService phoneValidationService;
    
    private static final Pattern INDIAN_PHONE_PATTERN = Pattern.compile(
        "^(\\+91[\\-\\s]?)?[0]?(91)?[789]\\d{9}$"
    );
    
    @Override
    public void apply(UserRegistrationContext context) {
        String phoneNumber = context.getRequest().getPhoneNumber();
        
        if (phoneNumber == null || phoneNumber.trim().isEmpty()) {
            return; // Phone number is optional
        }
        
        // Basic format validation
        String cleanPhone = phoneNumber.replaceAll("[\\s\\-\\(\\)]", "");
        if (!INDIAN_PHONE_PATTERN.matcher(cleanPhone).matches()) {
            throw new BusinessException("Invalid phone number format", "INVALID_PHONE_FORMAT");
        }
        
        // Advanced validation using external service
        try {
            PhoneValidationService.PhoneValidationResult result = phoneValidationService.validatePhone(cleanPhone);
            
            if (!result.isValid()) {
                throw new BusinessException("Phone number is not valid", "INVALID_PHONE_NUMBER");
            }
            
            if (result.isDisposable()) {
                throw new BusinessException("Disposable phone numbers are not allowed", "DISPOSABLE_PHONE_BLOCKED");
            }
            
            context.addEnrichmentData("phoneCarrier", result.getCarrier());
            context.addEnrichmentData("phoneType", result.getType());
            context.addEnrichmentData("phoneCountry", result.getCountry());
            
        } catch (Exception e) {
            log.warn("Phone validation service failed for: {}", cleanPhone, e);
            // Continue with basic validation if external service fails
        }
        
        log.debug("Phone validation passed for: {}", cleanPhone);
    }
    
    @Override
    public int getOrder() {
        return 2;
    }
}

/**
 * Performs fraud detection and risk assessment
 */
@Component
@org.springframework.core.annotation.Order(3)
@RequiredArgsConstructor
@Slf4j
class FraudDetectionStrategy implements UserRegistrationStrategy {
    
    private final FraudDetectionService fraudDetectionService;
    
    @Override
    public void apply(UserRegistrationContext context) {
        try {
            FraudDetectionService.FraudAssessment assessment = fraudDetectionService.assessRegistration(
                context.getRequest().getEmail(),
                context.getClientIpAddress(),
                context.getUserAgent(),
                context.getRequest().getPhoneNumber()
            );
            
            if (assessment.getRiskScore() > 0.8) {
                throw new BusinessException("Registration blocked due to high risk score", "HIGH_RISK_REGISTRATION");
            }
            
            if (assessment.isKnownFraudster()) {
                throw new BusinessException("Registration blocked", "FRAUDULENT_REGISTRATION");
            }
            
            context.addEnrichmentData("riskScore", assessment.getRiskScore());
            context.addEnrichmentData("riskFactors", assessment.getRiskFactors());
            context.addEnrichmentData("deviceFingerprint", assessment.getDeviceFingerprint());
            
            log.debug("Fraud detection passed with risk score: {}", assessment.getRiskScore());
            
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.warn("Fraud detection service failed", e);
            // Continue registration if fraud detection service is unavailable
        }
    }
    
    @Override
    public int getOrder() {
        return 3;
    }
}

/**
 * Enriches user data with geolocation information
 */
@Component
@org.springframework.core.annotation.Order(4)
@RequiredArgsConstructor
@Slf4j
class GeolocationEnrichmentStrategy implements UserRegistrationStrategy {
    
    private final GeolocationService geolocationService;
    
    @Override
    public void apply(UserRegistrationContext context) {
        String ipAddress = context.getClientIpAddress();
        
        if (ipAddress == null || ipAddress.isEmpty()) {
            return;
        }
        
        try {
            GeolocationService.LocationInfo locationInfo = geolocationService.getLocationInfo(ipAddress);
            
            context.addEnrichmentData("registrationCountry", locationInfo.getCountry());
            context.addEnrichmentData("registrationRegion", locationInfo.getRegion());
            context.addEnrichmentData("registrationCity", locationInfo.getCity());
            context.addEnrichmentData("registrationTimezone", locationInfo.getTimezone());
            context.addEnrichmentData("registrationIsp", locationInfo.getIsp());
            
            // Validate location consistency
            String requestCountry = context.getRequest().getCountry();
            if (requestCountry != null && !requestCountry.equalsIgnoreCase(locationInfo.getCountry())) {
                log.warn("Location mismatch: Request country {} vs IP country {}", 
                        requestCountry, locationInfo.getCountry());
                context.addEnrichmentData("locationMismatch", true);
            }
            
            log.debug("Geolocation enrichment completed for IP: {}", ipAddress);
            
        } catch (Exception e) {
            log.warn("Geolocation service failed for IP: {}", ipAddress, e);
            // Continue registration if geolocation service is unavailable
        }
    }
    
    @Override
    public int getOrder() {
        return 4;
    }
}

/**
 * Validates age requirements and date of birth
 */
@Component
@org.springframework.core.annotation.Order(5)
@Slf4j
class AgeValidationStrategy implements UserRegistrationStrategy {
    
    private static final int MIN_AGE = 18;
    private static final int MAX_AGE = 100;
    
    @Override
    public void apply(UserRegistrationContext context) {
        java.time.LocalDate dateOfBirth = context.getRequest().getDateOfBirth();
        
        if (dateOfBirth == null) {
            return; // Date of birth is optional
        }
        
        java.time.LocalDate now = java.time.LocalDate.now();
        int age = java.time.Period.between(dateOfBirth, now).getYears();
        
        if (age < MIN_AGE) {
            throw new BusinessException("You must be at least " + MIN_AGE + " years old to register", "UNDERAGE");
        }
        
        if (age > MAX_AGE) {
            throw new BusinessException("Invalid date of birth", "INVALID_DATE_OF_BIRTH");
        }
        
        // Check for future dates
        if (dateOfBirth.isAfter(now)) {
            throw new BusinessException("Date of birth cannot be in the future", "FUTURE_DATE_OF_BIRTH");
        }
        
        context.addEnrichmentData("userAge", age);
        context.addEnrichmentData("ageGroup", getAgeGroup(age));
        
        log.debug("Age validation passed for age: {}", age);
    }
    
    private String getAgeGroup(int age) {
        if (age < 25) return "18-24";
        if (age < 35) return "25-34";
        if (age < 45) return "35-44";
        if (age < 55) return "45-54";
        if (age < 65) return "55-64";
        return "65+";
    }
    
    @Override
    public int getOrder() {
        return 5;
    }
}

/**
 * Applies rate limiting for registration attempts
 */
@Component
@org.springframework.core.annotation.Order(6)
@Slf4j
class RateLimitingStrategy implements UserRegistrationStrategy {
    
    private final java.util.Map<String, java.util.List<LocalDateTime>> ipAttempts = new java.util.concurrent.ConcurrentHashMap<>();
    private final java.util.Map<String, java.util.List<LocalDateTime>> emailAttempts = new java.util.concurrent.ConcurrentHashMap<>();
    
    private static final int MAX_ATTEMPTS_PER_IP = 5;
    private static final int MAX_ATTEMPTS_PER_EMAIL = 3;
    private static final int RATE_LIMIT_WINDOW_MINUTES = 60;
    
    @Override
    public void apply(UserRegistrationContext context) {
        String ipAddress = context.getClientIpAddress();
        String email = context.getRequest().getEmail();
        LocalDateTime now = LocalDateTime.now();
        
        // Check IP-based rate limiting
        if (ipAddress != null) {
            checkRateLimit(ipAttempts, ipAddress, now, MAX_ATTEMPTS_PER_IP, "IP_RATE_LIMIT_EXCEEDED");
        }
        
        // Check email-based rate limiting
        if (email != null) {
            checkRateLimit(emailAttempts, email, now, MAX_ATTEMPTS_PER_EMAIL, "EMAIL_RATE_LIMIT_EXCEEDED");
        }
        
        // Record this attempt
        recordAttempt(ipAttempts, ipAddress, now);
        recordAttempt(emailAttempts, email, now);
        
        log.debug("Rate limiting check passed for IP: {} and email: {}", ipAddress, email);
    }
    
    private void checkRateLimit(java.util.Map<String, java.util.List<LocalDateTime>> attempts, 
                               String key, LocalDateTime now, int maxAttempts, String errorCode) {
        if (key == null) return;
        
        java.util.List<LocalDateTime> keyAttempts = attempts.computeIfAbsent(key, k -> new java.util.ArrayList<>());
        
        // Remove old attempts outside the window
        keyAttempts.removeIf(attempt -> attempt.isBefore(now.minusMinutes(RATE_LIMIT_WINDOW_MINUTES)));
        
        if (keyAttempts.size() >= maxAttempts) {
            throw new BusinessException("Too many registration attempts. Please try again later.", errorCode);
        }
    }
    
    private void recordAttempt(java.util.Map<String, java.util.List<LocalDateTime>> attempts, 
                              String key, LocalDateTime now) {
        if (key == null) return;
        
        attempts.computeIfAbsent(key, k -> new java.util.ArrayList<>()).add(now);
    }
    
    @Override
    public int getOrder() {
        return 6;
    }
}
