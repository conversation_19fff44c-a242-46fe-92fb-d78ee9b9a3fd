package com.wedding.user.service.impl;

import com.wedding.shared.dto.ApiResponse;
import com.wedding.shared.exception.BusinessException;
import com.wedding.shared.exception.ResourceNotFoundException;
import com.wedding.shared.security.UserPrincipal;
import com.wedding.shared.util.EmailService;
import com.wedding.user.dto.*;
import com.wedding.user.model.Role;
import com.wedding.user.model.User;
import com.wedding.user.model.VerificationToken;
import com.wedding.user.repository.UserRepository;
import com.wedding.user.repository.RoleRepository;
import com.wedding.user.repository.VerificationTokenRepository;
import com.wedding.user.service.UserService;
import com.wedding.user.service.strategy.PasswordValidationStrategy;
import com.wedding.user.service.strategy.UserRegistrationStrategy;
import com.wedding.user.event.UserEventPublisher;
import com.wedding.user.mapper.UserMapper;
import com.wedding.user.cache.UserCacheManager;
import com.wedding.user.audit.UserAuditService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * Advanced User Service Implementation with enterprise patterns
 * Features: Strategy Pattern, Event Sourcing, Caching, Async Processing, Audit Trail
 */
@Service
@Transactional(readOnly = true)
@RequiredArgsConstructor
@Validated
@Slf4j
public class AdvancedUserServiceImpl implements UserService {
    
    private final UserRepository userRepository;
    private final RoleRepository roleRepository;
    private final VerificationTokenRepository verificationTokenRepository;
    private final PasswordEncoder passwordEncoder;
    private final EmailService emailService;
    private final UserMapper userMapper;
    private final UserCacheManager cacheManager;
    private final UserAuditService auditService;
    private final UserEventPublisher eventPublisher;
    private final List<PasswordValidationStrategy> passwordValidationStrategies;
    private final List<UserRegistrationStrategy> registrationStrategies;
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, isolation = Isolation.READ_COMMITTED)
    @Retryable(value = {Exception.class}, maxAttempts = 3, backoff = @Backoff(delay = 1000))
    public ApiResponse<UserResponse> registerUser(@Valid @NotNull UserRegistrationRequest request) {
        log.info("Starting user registration for email: {}", request.getEmail());
        
        try {
            // Validate business rules
            validateUserRegistration(request);
            
            // Apply registration strategies
            UserRegistrationContext context = new UserRegistrationContext(request);
            registrationStrategies.forEach(strategy -> strategy.apply(context));
            
            // Create user entity
            User user = createUserFromRequest(request);
            
            // Save user with optimistic locking
            User savedUser = userRepository.save(user);
            
            // Generate verification token
            VerificationToken verificationToken = createVerificationToken(savedUser);
            
            // Async operations
            CompletableFuture.allOf(
                sendWelcomeEmailAsync(savedUser, verificationToken),
                publishUserRegisteredEventAsync(savedUser),
                updateUserCacheAsync(savedUser)
            ).join();
            
            // Audit trail
            auditService.logUserRegistration(savedUser, request.getRegistrationSource());
            
            UserResponse response = userMapper.toResponse(savedUser);
            
            log.info("User registration completed successfully for: {}", request.getEmail());
            return ApiResponse.success(response, "User registered successfully. Please check your email for verification.");
            
        } catch (Exception e) {
            log.error("User registration failed for email: {}", request.getEmail(), e);
            auditService.logUserRegistrationFailure(request.getEmail(), e.getMessage());
            throw new BusinessException("Registration failed: " + e.getMessage(), "REGISTRATION_FAILED");
        }
    }
    
    @Override
    @Cacheable(value = "users", key = "#userId", unless = "#result == null")
    public ApiResponse<UserResponse> getUserById(@NotNull String userId) {
        log.debug("Fetching user by ID: {}", userId);
        
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new ResourceNotFoundException("User", userId));
        
        // Check cache first
        Optional<UserResponse> cachedUser = cacheManager.getUser(userId);
        if (cachedUser.isPresent()) {
            log.debug("User found in cache: {}", userId);
            return ApiResponse.success(cachedUser.get());
        }
        
        UserResponse response = userMapper.toResponse(user);
        
        // Update cache asynchronously
        cacheManager.putUserAsync(userId, response);
        
        return ApiResponse.success(response);
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    @CacheEvict(value = "users", key = "#userId")
    public ApiResponse<UserResponse> updateUser(@NotNull String userId, @Valid UserUpdateRequest request) {
        log.info("Updating user: {}", userId);
        
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new ResourceNotFoundException("User", userId));
        
        // Create audit snapshot before changes
        UserSnapshot beforeSnapshot = auditService.createSnapshot(user);
        
        // Apply updates with validation
        updateUserFields(user, request);
        
        // Save with optimistic locking
        User updatedUser = userRepository.save(user);
        
        // Create audit snapshot after changes
        UserSnapshot afterSnapshot = auditService.createSnapshot(updatedUser);
        
        // Async operations
        CompletableFuture.allOf(
            publishUserUpdatedEventAsync(updatedUser, beforeSnapshot, afterSnapshot),
            updateUserCacheAsync(updatedUser),
            sendProfileUpdateNotificationAsync(updatedUser)
        ).join();
        
        UserResponse response = userMapper.toResponse(updatedUser);
        
        log.info("User updated successfully: {}", userId);
        return ApiResponse.success(response, "User profile updated successfully");
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public ApiResponse<Void> verifyEmail(@NotNull String token) {
        log.info("Verifying email with token: {}", token);
        
        VerificationToken verificationToken = verificationTokenRepository.findByToken(token)
                .orElseThrow(() -> new BusinessException("Invalid verification token", "INVALID_TOKEN"));
        
        if (verificationToken.isExpired()) {
            throw new BusinessException("Verification token has expired", "TOKEN_EXPIRED");
        }
        
        User user = verificationToken.getUser();
        user.setIsVerified(true);
        user.setVerificationToken(null);
        user.setVerificationTokenExpiresAt(null);
        
        userRepository.save(user);
        verificationTokenRepository.delete(verificationToken);
        
        // Async operations
        CompletableFuture.allOf(
            publishEmailVerifiedEventAsync(user),
            sendEmailVerificationConfirmationAsync(user),
            updateUserCacheAsync(user)
        ).join();
        
        auditService.logEmailVerification(user);
        
        log.info("Email verified successfully for user: {}", user.getId());
        return ApiResponse.success(null, "Email verified successfully");
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public ApiResponse<Void> changePassword(@NotNull String userId, @Valid ChangePasswordRequest request) {
        log.info("Changing password for user: {}", userId);
        
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new ResourceNotFoundException("User", userId));
        
        // Validate current password
        if (!passwordEncoder.matches(request.getCurrentPassword(), user.getPassword())) {
            auditService.logPasswordChangeFailure(user, "Invalid current password");
            throw new BusinessException("Current password is incorrect", "INVALID_PASSWORD");
        }
        
        // Apply password validation strategies
        PasswordValidationContext context = new PasswordValidationContext(request.getNewPassword(), user);
        passwordValidationStrategies.forEach(strategy -> strategy.validate(context));
        
        // Update password
        user.setPassword(passwordEncoder.encode(request.getNewPassword()));
        user.setPasswordChangedAt(LocalDateTime.now());
        user.resetFailedLoginAttempts();
        
        userRepository.save(user);
        
        // Async operations
        CompletableFuture.allOf(
            publishPasswordChangedEventAsync(user),
            sendPasswordChangeNotificationAsync(user),
            invalidateUserSessionsAsync(user)
        ).join();
        
        auditService.logPasswordChange(user);
        
        log.info("Password changed successfully for user: {}", userId);
        return ApiResponse.success(null, "Password changed successfully");
    }
    
    @Override
    public ApiResponse<Page<UserResponse>> searchUsers(@NotNull UserSearchCriteria criteria, @NotNull Pageable pageable) {
        log.debug("Searching users with criteria: {}", criteria);
        
        Page<User> users = userRepository.findByCriteria(criteria, pageable);
        Page<UserResponse> responses = users.map(userMapper::toResponse);
        
        return ApiResponse.successWithPagination(responses, 
                ApiResponse.PaginationInfo.of(pageable.getPageNumber(), pageable.getPageSize(), users.getTotalElements()));
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public ApiResponse<Void> lockUser(@NotNull String userId, @NotNull String reason) {
        log.info("Locking user: {} with reason: {}", userId, reason);
        
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new ResourceNotFoundException("User", userId));
        
        user.setIsActive(false);
        user.setLockedUntil(LocalDateTime.now().plusDays(30)); // 30 days lock
        
        userRepository.save(user);
        
        // Async operations
        CompletableFuture.allOf(
            publishUserLockedEventAsync(user, reason),
            sendAccountLockedNotificationAsync(user, reason),
            invalidateUserSessionsAsync(user),
            updateUserCacheAsync(user)
        ).join();
        
        auditService.logUserLocked(user, reason);
        
        log.info("User locked successfully: {}", userId);
        return ApiResponse.success(null, "User account locked successfully");
    }
    
    // Private helper methods
    
    private void validateUserRegistration(UserRegistrationRequest request) {
        if (userRepository.existsByEmail(request.getEmail())) {
            throw new BusinessException("Email already exists", "EMAIL_EXISTS");
        }
        
        if (request.getPhoneNumber() != null && userRepository.existsByPhoneNumber(request.getPhoneNumber())) {
            throw new BusinessException("Phone number already exists", "PHONE_EXISTS");
        }
    }
    
    private User createUserFromRequest(UserRegistrationRequest request) {
        Role defaultRole = roleRepository.findByName(Role.RoleName.ROLE_CUSTOMER)
                .orElseThrow(() -> new BusinessException("Default role not found", "ROLE_NOT_FOUND"));
        
        return User.builder()
                .id(UUID.randomUUID().toString())
                .email(request.getEmail())
                .password(passwordEncoder.encode(request.getPassword()))
                .firstName(request.getFirstName())
                .lastName(request.getLastName())
                .phoneNumber(request.getPhoneNumber())
                .dateOfBirth(request.getDateOfBirth())
                .city(request.getCity())
                .state(request.getState())
                .country(request.getCountry())
                .isActive(true)
                .isVerified(false)
                .roles(Set.of(defaultRole))
                .createdAt(LocalDateTime.now())
                .build();
    }
    
    private VerificationToken createVerificationToken(User user) {
        String token = UUID.randomUUID().toString();
        VerificationToken verificationToken = VerificationToken.builder()
                .token(token)
                .user(user)
                .expiresAt(LocalDateTime.now().plusHours(24))
                .build();
        
        return verificationTokenRepository.save(verificationToken);
    }
    
    private void updateUserFields(User user, UserUpdateRequest request) {
        if (request.getFirstName() != null) {
            user.setFirstName(request.getFirstName());
        }
        if (request.getLastName() != null) {
            user.setLastName(request.getLastName());
        }
        if (request.getPhoneNumber() != null) {
            user.setPhoneNumber(request.getPhoneNumber());
        }
        if (request.getDateOfBirth() != null) {
            user.setDateOfBirth(request.getDateOfBirth());
        }
        if (request.getCity() != null) {
            user.setCity(request.getCity());
        }
        if (request.getState() != null) {
            user.setState(request.getState());
        }
        if (request.getProfileImageUrl() != null) {
            user.setProfileImageUrl(request.getProfileImageUrl());
        }
        
        user.setUpdatedAt(LocalDateTime.now());
    }
    
    // Async methods
    
    private CompletableFuture<Void> sendWelcomeEmailAsync(User user, VerificationToken token) {
        return emailService.sendVerificationEmail(user.getEmail(), user.getFirstName(), token.getToken())
                .thenRun(() -> log.debug("Welcome email sent to: {}", user.getEmail()));
    }
    
    private CompletableFuture<Void> publishUserRegisteredEventAsync(User user) {
        return CompletableFuture.runAsync(() -> eventPublisher.publishUserRegisteredEvent(user));
    }
    
    private CompletableFuture<Void> updateUserCacheAsync(User user) {
        return CompletableFuture.runAsync(() -> {
            UserResponse response = userMapper.toResponse(user);
            cacheManager.putUser(user.getId(), response);
        });
    }
    
    private CompletableFuture<Void> publishUserUpdatedEventAsync(User user, UserSnapshot before, UserSnapshot after) {
        return CompletableFuture.runAsync(() -> eventPublisher.publishUserUpdatedEvent(user, before, after));
    }
    
    private CompletableFuture<Void> sendProfileUpdateNotificationAsync(User user) {
        return emailService.sendSimpleEmail(user.getEmail(), "Profile Updated", 
                "Your profile has been updated successfully.")
                .thenRun(() -> log.debug("Profile update notification sent to: {}", user.getEmail()));
    }
    
    private CompletableFuture<Void> publishEmailVerifiedEventAsync(User user) {
        return CompletableFuture.runAsync(() -> eventPublisher.publishEmailVerifiedEvent(user));
    }
    
    private CompletableFuture<Void> sendEmailVerificationConfirmationAsync(User user) {
        return emailService.sendSimpleEmail(user.getEmail(), "Email Verified", 
                "Your email has been verified successfully.")
                .thenRun(() -> log.debug("Email verification confirmation sent to: {}", user.getEmail()));
    }
    
    private CompletableFuture<Void> publishPasswordChangedEventAsync(User user) {
        return CompletableFuture.runAsync(() -> eventPublisher.publishPasswordChangedEvent(user));
    }
    
    private CompletableFuture<Void> sendPasswordChangeNotificationAsync(User user) {
        return emailService.sendSimpleEmail(user.getEmail(), "Password Changed", 
                "Your password has been changed successfully.")
                .thenRun(() -> log.debug("Password change notification sent to: {}", user.getEmail()));
    }
    
    private CompletableFuture<Void> invalidateUserSessionsAsync(User user) {
        return CompletableFuture.runAsync(() -> cacheManager.invalidateUserSessions(user.getId()));
    }
    
    private CompletableFuture<Void> publishUserLockedEventAsync(User user, String reason) {
        return CompletableFuture.runAsync(() -> eventPublisher.publishUserLockedEvent(user, reason));
    }
    
    private CompletableFuture<Void> sendAccountLockedNotificationAsync(User user, String reason) {
        return emailService.sendSimpleEmail(user.getEmail(), "Account Locked", 
                "Your account has been locked. Reason: " + reason)
                .thenRun(() -> log.debug("Account locked notification sent to: {}", user.getEmail()));
    }
}
