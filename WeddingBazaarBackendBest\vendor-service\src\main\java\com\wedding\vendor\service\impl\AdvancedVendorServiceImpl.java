package com.wedding.vendor.service.impl;

import com.wedding.shared.dto.ApiResponse;
import com.wedding.shared.exception.BusinessException;
import com.wedding.shared.exception.ResourceNotFoundException;
import com.wedding.vendor.dto.*;
import com.wedding.vendor.model.Vendor;
import com.wedding.vendor.model.VendorService;
import com.wedding.vendor.repository.VendorRepository;
import com.wedding.vendor.repository.VendorServiceRepository;
import com.wedding.vendor.service.VendorService as VendorServiceInterface;
import com.wedding.vendor.service.strategy.VendorVerificationStrategy;
import com.wedding.vendor.service.strategy.PricingStrategy;
import com.wedding.vendor.service.strategy.RecommendationStrategy;
import com.wedding.vendor.event.VendorEventPublisher;
import com.wedding.vendor.mapper.VendorMapper;
import com.wedding.vendor.cache.VendorCacheManager;
import com.wedding.vendor.analytics.VendorAnalyticsService;
import com.wedding.vendor.ml.VendorRecommendationEngine;
import com.wedding.vendor.workflow.VendorWorkflowEngine;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;

/**
 * Advanced Vendor Service Implementation with enterprise patterns
 * Features: ML-based recommendations, workflow engine, advanced analytics, dynamic pricing
 */
@Service
@Transactional(readOnly = true)
@RequiredArgsConstructor
@Validated
@Slf4j
public class AdvancedVendorServiceImpl implements VendorServiceInterface {
    
    private final VendorRepository vendorRepository;
    private final VendorServiceRepository vendorServiceRepository;
    private final VendorMapper vendorMapper;
    private final VendorCacheManager cacheManager;
    private final VendorAnalyticsService analyticsService;
    private final VendorEventPublisher eventPublisher;
    private final VendorRecommendationEngine recommendationEngine;
    private final VendorWorkflowEngine workflowEngine;
    private final List<VendorVerificationStrategy> verificationStrategies;
    private final List<PricingStrategy> pricingStrategies;
    private final List<RecommendationStrategy> recommendationStrategies;
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, isolation = Isolation.READ_COMMITTED)
    @Retryable(value = {Exception.class}, maxAttempts = 3, backoff = @Backoff(delay = 1000))
    public ApiResponse<VendorResponse> createVendor(@Valid @NotNull VendorCreateRequest request) {
        log.info("Creating vendor for user: {}", request.getUserId());
        
        try {
            // Validate business rules
            validateVendorCreation(request);
            
            // Apply verification strategies
            VendorVerificationContext verificationContext = new VendorVerificationContext(request);
            verificationStrategies.forEach(strategy -> strategy.apply(verificationContext));
            
            // Create vendor entity
            Vendor vendor = createVendorFromRequest(request, verificationContext);
            
            // Save vendor with optimistic locking
            Vendor savedVendor = vendorRepository.save(vendor);
            
            // Initialize workflow
            workflowEngine.initializeVendorWorkflow(savedVendor);
            
            // Async operations
            CompletableFuture.allOf(
                publishVendorCreatedEventAsync(savedVendor),
                initializeVendorAnalyticsAsync(savedVendor),
                updateVendorCacheAsync(savedVendor),
                triggerVerificationProcessAsync(savedVendor)
            ).join();
            
            VendorResponse response = vendorMapper.toResponse(savedVendor);
            
            log.info("Vendor created successfully: {}", savedVendor.getId());
            return ApiResponse.success(response, "Vendor profile created successfully");
            
        } catch (Exception e) {
            log.error("Vendor creation failed for user: {}", request.getUserId(), e);
            throw new BusinessException("Vendor creation failed: " + e.getMessage(), "VENDOR_CREATION_FAILED");
        }
    }
    
    @Override
    @Cacheable(value = "vendors", key = "#vendorId", unless = "#result == null")
    public ApiResponse<VendorResponse> getVendor(@NotNull String vendorId) {
        log.debug("Fetching vendor: {}", vendorId);
        
        // Check cache first
        Optional<VendorResponse> cachedVendor = cacheManager.getVendor(vendorId);
        if (cachedVendor.isPresent()) {
            log.debug("Vendor found in cache: {}", vendorId);
            return ApiResponse.success(cachedVendor.get());
        }
        
        Vendor vendor = vendorRepository.findById(vendorId)
                .orElseThrow(() -> new ResourceNotFoundException("Vendor", vendorId));
        
        // Record view for analytics
        analyticsService.recordVendorView(vendorId);
        
        VendorResponse response = vendorMapper.toResponse(vendor);
        
        // Update cache asynchronously
        cacheManager.putVendorAsync(vendorId, response);
        
        return ApiResponse.success(response);
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    @CacheEvict(value = "vendors", key = "#vendorId")
    public ApiResponse<VendorResponse> updateVendor(@NotNull String vendorId, @Valid VendorUpdateRequest request) {
        log.info("Updating vendor: {}", vendorId);
        
        Vendor vendor = vendorRepository.findById(vendorId)
                .orElseThrow(() -> new ResourceNotFoundException("Vendor", vendorId));
        
        // Create snapshot for audit
        VendorSnapshot beforeSnapshot = createVendorSnapshot(vendor);
        
        // Apply updates with validation
        updateVendorFields(vendor, request);
        
        // Re-evaluate verification status if needed
        if (requiresReverification(request)) {
            vendor.setVerificationStatus(Vendor.VerificationStatus.UNDER_REVIEW);
            workflowEngine.triggerReverification(vendor);
        }
        
        // Save with optimistic locking
        Vendor updatedVendor = vendorRepository.save(vendor);
        
        // Create snapshot after update
        VendorSnapshot afterSnapshot = createVendorSnapshot(updatedVendor);
        
        // Async operations
        CompletableFuture.allOf(
            publishVendorUpdatedEventAsync(updatedVendor, beforeSnapshot, afterSnapshot),
            updateVendorCacheAsync(updatedVendor),
            updateSearchIndexAsync(updatedVendor),
            recalculateRecommendationsAsync(updatedVendor)
        ).join();
        
        VendorResponse response = vendorMapper.toResponse(updatedVendor);
        
        log.info("Vendor updated successfully: {}", vendorId);
        return ApiResponse.success(response, "Vendor profile updated successfully");
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public ApiResponse<VendorServiceResponse> addService(@NotNull String vendorId, @Valid VendorServiceCreateRequest request) {
        log.info("Adding service to vendor: {}", vendorId);
        
        Vendor vendor = vendorRepository.findById(vendorId)
                .orElseThrow(() -> new ResourceNotFoundException("Vendor", vendorId));
        
        // Validate service limits based on subscription
        validateServiceLimits(vendor, request);
        
        // Apply pricing strategies
        PricingContext pricingContext = new PricingContext(request, vendor);
        pricingStrategies.forEach(strategy -> strategy.apply(pricingContext));
        
        // Create service entity
        VendorService service = createServiceFromRequest(request, vendor, pricingContext);
        
        // Save service
        VendorService savedService = vendorServiceRepository.save(service);
        
        // Update vendor statistics
        updateVendorServiceStatistics(vendor);
        
        // Async operations
        CompletableFuture.allOf(
            publishServiceAddedEventAsync(vendor, savedService),
            updateSearchIndexAsync(vendor),
            updateVendorCacheAsync(vendor)
        ).join();
        
        VendorServiceResponse response = vendorMapper.toServiceResponse(savedService);
        
        log.info("Service added successfully: {} to vendor: {}", savedService.getId(), vendorId);
        return ApiResponse.success(response, "Service added successfully");
    }
    
    @Override
    public ApiResponse<List<VendorResponse>> getRecommendedVendors(@NotNull String userId, @NotNull VendorRecommendationRequest request) {
        log.debug("Getting vendor recommendations for user: {}", userId);
        
        try {
            // Apply recommendation strategies
            RecommendationContext context = new RecommendationContext(userId, request);
            recommendationStrategies.forEach(strategy -> strategy.apply(context));
            
            // Use ML-based recommendation engine
            List<String> recommendedVendorIds = recommendationEngine.getRecommendations(context);
            
            // Fetch vendor details
            List<Vendor> vendors = vendorRepository.findAllById(recommendedVendorIds);
            
            // Apply business rules and filtering
            List<Vendor> filteredVendors = applyRecommendationFilters(vendors, request);
            
            // Sort by recommendation score
            List<Vendor> sortedVendors = sortByRecommendationScore(filteredVendors, context);
            
            List<VendorResponse> responses = sortedVendors.stream()
                    .map(vendorMapper::toResponse)
                    .toList();
            
            // Record recommendation event for analytics
            analyticsService.recordRecommendationEvent(userId, recommendedVendorIds);
            
            log.debug("Generated {} recommendations for user: {}", responses.size(), userId);
            return ApiResponse.success(responses);
            
        } catch (Exception e) {
            log.error("Failed to generate recommendations for user: {}", userId, e);
            return ApiResponse.error("Failed to generate recommendations");
        }
    }
    
    @Override
    public ApiResponse<VendorAnalyticsResponse> getVendorAnalytics(@NotNull String vendorId, @NotNull AnalyticsRequest request) {
        log.debug("Getting analytics for vendor: {}", vendorId);
        
        Vendor vendor = vendorRepository.findById(vendorId)
                .orElseThrow(() -> new ResourceNotFoundException("Vendor", vendorId));
        
        VendorAnalyticsResponse analytics = analyticsService.generateVendorAnalytics(vendor, request);
        
        return ApiResponse.success(analytics);
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public ApiResponse<Void> verifyVendor(@NotNull String vendorId, @Valid VendorVerificationRequest request) {
        log.info("Verifying vendor: {}", vendorId);
        
        Vendor vendor = vendorRepository.findById(vendorId)
                .orElseThrow(() -> new ResourceNotFoundException("Vendor", vendorId));
        
        // Execute verification workflow
        workflowEngine.executeVerificationWorkflow(vendor, request);
        
        // Update verification status
        vendor.setVerificationStatus(request.isApproved() ? 
                Vendor.VerificationStatus.VERIFIED : Vendor.VerificationStatus.REJECTED);
        vendor.setVerificationNotes(request.getNotes());
        vendor.setVerifiedAt(LocalDateTime.now());
        vendor.setVerifiedBy(request.getVerifiedBy());
        
        vendorRepository.save(vendor);
        
        // Async operations
        CompletableFuture.allOf(
            publishVendorVerificationEventAsync(vendor, request),
            updateVendorCacheAsync(vendor),
            updateSearchIndexAsync(vendor),
            sendVerificationNotificationAsync(vendor, request.isApproved())
        ).join();
        
        log.info("Vendor verification completed: {} - Status: {}", vendorId, vendor.getVerificationStatus());
        return ApiResponse.success(null, "Vendor verification completed");
    }
    
    @Override
    public ApiResponse<Page<VendorResponse>> searchVendors(@NotNull VendorSearchCriteria criteria, @NotNull Pageable pageable) {
        log.debug("Searching vendors with criteria: {}", criteria);
        
        Page<Vendor> vendors = vendorRepository.findByCriteria(criteria, pageable);
        
        // Apply dynamic scoring and ranking
        Page<VendorResponse> responses = vendors.map(vendor -> {
            VendorResponse response = vendorMapper.toResponse(vendor);
            response.setRecommendationScore(calculateDynamicScore(vendor, criteria));
            return response;
        });
        
        // Record search event for analytics
        analyticsService.recordSearchEvent(criteria);
        
        return ApiResponse.successWithPagination(responses, 
                ApiResponse.PaginationInfo.of(pageable.getPageNumber(), pageable.getPageSize(), vendors.getTotalElements()));
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public ApiResponse<Void> updateSubscription(@NotNull String vendorId, @NotNull SubscriptionUpdateRequest request) {
        log.info("Updating subscription for vendor: {}", vendorId);
        
        Vendor vendor = vendorRepository.findById(vendorId)
                .orElseThrow(() -> new ResourceNotFoundException("Vendor", vendorId));
        
        // Validate subscription change
        validateSubscriptionChange(vendor, request);
        
        // Update subscription
        vendor.setSubscriptionPlan(request.getNewPlan());
        vendor.setSubscriptionExpiresAt(request.getExpiresAt());
        
        vendorRepository.save(vendor);
        
        // Async operations
        CompletableFuture.allOf(
            publishSubscriptionUpdatedEventAsync(vendor, request),
            updateVendorCacheAsync(vendor),
            recalculateServiceLimitsAsync(vendor)
        ).join();
        
        log.info("Subscription updated for vendor: {} to plan: {}", vendorId, request.getNewPlan());
        return ApiResponse.success(null, "Subscription updated successfully");
    }
    
    // Private helper methods
    
    private void validateVendorCreation(VendorCreateRequest request) {
        if (vendorRepository.existsByUserId(request.getUserId())) {
            throw new BusinessException("Vendor profile already exists for this user", "VENDOR_EXISTS");
        }
        
        if (vendorRepository.existsByBusinessEmail(request.getBusinessEmail())) {
            throw new BusinessException("Business email already registered", "BUSINESS_EMAIL_EXISTS");
        }
        
        if (request.getGstNumber() != null && vendorRepository.existsByGstNumber(request.getGstNumber())) {
            throw new BusinessException("GST number already registered", "GST_NUMBER_EXISTS");
        }
    }
    
    private Vendor createVendorFromRequest(VendorCreateRequest request, VendorVerificationContext context) {
        return Vendor.builder()
                .userId(request.getUserId())
                .businessName(request.getBusinessName())
                .businessEmail(request.getBusinessEmail())
                .businessPhone(request.getBusinessPhone())
                .category(request.getCategory())
                .subcategories(request.getSubcategories())
                .city(request.getCity())
                .state(request.getState())
                .country(request.getCountry())
                .fullAddress(request.getFullAddress())
                .description(request.getDescription())
                .yearsOfExperience(request.getYearsOfExperience())
                .teamSize(request.getTeamSize())
                .startingPrice(request.getStartingPrice())
                .isActive(true)
                .isVerified(false)
                .verificationStatus(Vendor.VerificationStatus.PENDING)
                .subscriptionPlan(Vendor.SubscriptionPlan.FREE)
                .commissionRate(getDefaultCommissionRate())
                .createdAt(LocalDateTime.now())
                .build();
    }
    
    private void updateVendorFields(Vendor vendor, VendorUpdateRequest request) {
        if (request.getBusinessName() != null) {
            vendor.setBusinessName(request.getBusinessName());
        }
        if (request.getBusinessPhone() != null) {
            vendor.setBusinessPhone(request.getBusinessPhone());
        }
        if (request.getDescription() != null) {
            vendor.setDescription(request.getDescription());
        }
        if (request.getYearsOfExperience() != null) {
            vendor.setYearsOfExperience(request.getYearsOfExperience());
        }
        if (request.getTeamSize() != null) {
            vendor.setTeamSize(request.getTeamSize());
        }
        if (request.getStartingPrice() != null) {
            vendor.setStartingPrice(request.getStartingPrice());
        }
        if (request.getWebsiteUrl() != null) {
            vendor.setWebsiteUrl(request.getWebsiteUrl());
        }
        
        vendor.setUpdatedAt(LocalDateTime.now());
    }
    
    private boolean requiresReverification(VendorUpdateRequest request) {
        // Define fields that require re-verification when changed
        return request.getBusinessName() != null || 
               request.getBusinessPhone() != null ||
               request.getGstNumber() != null;
    }
    
    private void validateServiceLimits(Vendor vendor, VendorServiceCreateRequest request) {
        long currentServiceCount = vendorServiceRepository.countByVendorIdAndIsActiveTrue(vendor.getId());
        int maxServices = getMaxServicesForPlan(vendor.getSubscriptionPlan());
        
        if (currentServiceCount >= maxServices) {
            throw new BusinessException("Service limit exceeded for current subscription plan", "SERVICE_LIMIT_EXCEEDED");
        }
    }
    
    private VendorService createServiceFromRequest(VendorServiceCreateRequest request, Vendor vendor, PricingContext context) {
        return VendorService.builder()
                .vendorId(vendor.getId())
                .name(request.getName())
                .description(request.getDescription())
                .category(request.getCategory())
                .subcategory(request.getSubcategory())
                .basePrice(context.getOptimizedPrice())
                .maxPrice(request.getMaxPrice())
                .priceUnit(request.getPriceUnit())
                .features(request.getFeatures())
                .inclusions(request.getInclusions())
                .exclusions(request.getExclusions())
                .isActive(true)
                .createdAt(LocalDateTime.now())
                .build();
    }
    
    private BigDecimal getDefaultCommissionRate() {
        return new BigDecimal("10.00"); // 10% default
    }
    
    private int getMaxServicesForPlan(Vendor.SubscriptionPlan plan) {
        return switch (plan) {
            case FREE -> 3;
            case BASIC -> 10;
            case PREMIUM -> 50;
            case ENTERPRISE -> Integer.MAX_VALUE;
        };
    }
    
    // Async methods and other helper methods would continue here...
    
    private CompletableFuture<Void> publishVendorCreatedEventAsync(Vendor vendor) {
        return CompletableFuture.runAsync(() -> eventPublisher.publishVendorCreatedEvent(vendor));
    }
    
    private CompletableFuture<Void> initializeVendorAnalyticsAsync(Vendor vendor) {
        return CompletableFuture.runAsync(() -> analyticsService.initializeVendorAnalytics(vendor));
    }
    
    private CompletableFuture<Void> updateVendorCacheAsync(Vendor vendor) {
        return CompletableFuture.runAsync(() -> {
            VendorResponse response = vendorMapper.toResponse(vendor);
            cacheManager.putVendor(vendor.getId(), response);
        });
    }
    
    private CompletableFuture<Void> triggerVerificationProcessAsync(Vendor vendor) {
        return CompletableFuture.runAsync(() -> workflowEngine.triggerVerificationProcess(vendor));
    }
    
    // Additional helper methods would be implemented here...
}
