server:
  port: 8080

spring:
  application:
    name: api-gateway
  cloud:
    gateway:
      routes:
        # User Service Routes
        - id: user-service
          uri: lb://user-service
          predicates:
            - Path=/api/users/**,/api/auth/**
          filters:
            - name: JwtAuthenticationFilter
            - name: CircuitBreaker
              args:
                name: user-service
                fallbackUri: forward:/fallback/user-service
        
        # Vendor Service Routes
        - id: vendor-service
          uri: lb://vendor-service
          predicates:
            - Path=/api/vendors/**
          filters:
            - name: JwtAuthenticationFilter
            - name: CircuitBreaker
              args:
                name: vendor-service
                fallbackUri: forward:/fallback/vendor-service
        
        # Booking Service Routes
        - id: booking-service
          uri: lb://booking-service
          predicates:
            - Path=/api/bookings/**
          filters:
            - name: JwtAuthenticationFilter
            - name: CircuitBreaker
              args:
                name: booking-service
                fallbackUri: forward:/fallback/booking-service
        
        # Search Service Routes
        - id: search-service
          uri: lb://search-service
          predicates:
            - Path=/api/search/**
          filters:
            - name: JwtAuthenticationFilter
            - name: CircuitBreaker
              args:
                name: search-service
                fallbackUri: forward:/fallback/search-service
        
        # Messaging Service Routes
        - id: messaging-service
          uri: lb://messaging-service
          predicates:
            - Path=/api/messages/**,/api/notifications/**
          filters:
            - name: JwtAuthenticationFilter
            - name: CircuitBreaker
              args:
                name: messaging-service
                fallbackUri: forward:/fallback/messaging-service
        
        # Analytics Service Routes
        - id: analytics-service
          uri: lb://analytics-service
          predicates:
            - Path=/api/analytics/**
          filters:
            - name: JwtAuthenticationFilter
            - name: CircuitBreaker
              args:
                name: analytics-service
                fallbackUri: forward:/fallback/analytics-service
        
        # Admin Service Routes
        - id: admin-service
          uri: lb://admin-service
          predicates:
            - Path=/api/admin/**
          filters:
            - name: JwtAuthenticationFilter
            - name: CircuitBreaker
              args:
                name: admin-service
                fallbackUri: forward:/fallback/admin-service
        
        # Payment Service Routes
        - id: payment-service
          uri: lb://payment-service
          predicates:
            - Path=/api/payments/**
          filters:
            - name: JwtAuthenticationFilter
            - name: CircuitBreaker
              args:
                name: payment-service
                fallbackUri: forward:/fallback/payment-service
      
      default-filters:
        - name: Retry
          args:
            retries: 3
            methods: GET,POST,PUT,DELETE
        - name: RequestRateLimiter
          args:
            redis-rate-limiter.replenishRate: 100
            redis-rate-limiter.burstCapacity: 200
            redis-rate-limiter.requestedTokens: 1
  
  redis:
    host: localhost
    port: 6379
    timeout: 2000ms

eureka:
  client:
    service-url:
      defaultZone: ************************************/eureka/
  instance:
    prefer-ip-address: true

management:
  endpoints:
    web:
      exposure:
        include: health,info,gateway,metrics
  endpoint:
    health:
      show-details: always
    gateway:
      enabled: true

resilience4j:
  circuitbreaker:
    instances:
      user-service:
        sliding-window-size: 10
        minimum-number-of-calls: 5
        failure-rate-threshold: 50
        wait-duration-in-open-state: 30s
      vendor-service:
        sliding-window-size: 10
        minimum-number-of-calls: 5
        failure-rate-threshold: 50
        wait-duration-in-open-state: 30s
      booking-service:
        sliding-window-size: 10
        minimum-number-of-calls: 5
        failure-rate-threshold: 50
        wait-duration-in-open-state: 30s

logging:
  level:
    org.springframework.cloud.gateway: DEBUG
    com.weddingbazaar.gateway: DEBUG
