apiVersion: v1
kind: Namespace
metadata:
  name: wedding-bazaar
  labels:
    name: wedding-bazaar
    environment: production
---
apiVersion: v1
kind: Secret
metadata:
  name: postgres-secret
  namespace: wedding-bazaar
type: Opaque
data:
  username: cG9zdGdyZXM= # postgres (base64 encoded)
  password: cG9zdGdyZXM= # postgres (base64 encoded)
---
apiVersion: v1
kind: Secret
metadata:
  name: jwt-secret
  namespace: wedding-bazaar
type: Opaque
data:
  secret: d2VkZGluZy1iYXphYXItc2VjcmV0LWtleS0yMDI0LXByb2R1Y3Rpb24= # wedding-bazaar-secret-key-2024-production (base64)
---
apiVersion: v1
kind: Secret
metadata:
  name: oauth2-secrets
  namespace: wedding-bazaar
type: Opaque
data:
  google-client-id: eW91ci1nb29nbGUtY2xpZW50LWlk # your-google-client-id (base64)
  google-client-secret: eW91ci1nb29nbGUtY2xpZW50LXNlY3JldA== # your-google-client-secret (base64)
  facebook-client-id: eW91ci1mYWNlYm9vay1jbGllbnQtaWQ= # your-facebook-client-id (base64)
  facebook-client-secret: eW91ci1mYWNlYm9vay1jbGllbnQtc2VjcmV0 # your-facebook-client-secret (base64)
---
apiVersion: v1
kind: Secret
metadata:
  name: payment-secrets
  namespace: wedding-bazaar
type: Opaque
data:
  stripe-public-key: cGtfdGVzdF95b3VyX3N0cmlwZV9wdWJsaWNfa2V5 # pk_test_your_stripe_public_key (base64)
  stripe-secret-key: c2tfdGVzdF95b3VyX3N0cmlwZV9zZWNyZXRfa2V5 # sk_test_your_stripe_secret_key (base64)
  stripe-webhook-secret: d2hzZWNfeW91cl93ZWJob29rX3NlY3JldA== # whsec_your_webhook_secret (base64)
  paypal-client-id: eW91ci1wYXlwYWwtY2xpZW50LWlk # your-paypal-client-id (base64)
  paypal-client-secret: eW91ci1wYXlwYWwtY2xpZW50LXNlY3JldA== # your-paypal-client-secret (base64)
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: app-config
  namespace: wedding-bazaar
data:
  frontend-url: "https://weddingbazaar.com"
  backend-url: "https://api.weddingbazaar.com"
  support-email: "<EMAIL>"
  admin-email: "<EMAIL>"
