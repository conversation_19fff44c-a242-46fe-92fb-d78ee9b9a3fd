#!/bin/bash

# Wedding Bazaar Backend - Setup Test Script
# This script tests if all prerequisites are properly installed

set -e

# Colors
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_header() {
    echo -e "${BLUE}"
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║                Wedding Bazaar Backend                        ║"
    echo "║                   Setup Test Script                          ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
}

print_success() {
    echo -e "${GREEN}✓ $1${NC}"
}

print_error() {
    echo -e "${RED}✗ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ $1${NC}"
}

# Test functions
test_java() {
    print_info "Testing Java installation..."
    
    if command -v java &> /dev/null; then
        local java_version=$(java -version 2>&1 | head -n 1 | cut -d'"' -f2 | cut -d'.' -f1)
        if [ "$java_version" -ge 17 ]; then
            print_success "Java $java_version found"
            return 0
        else
            print_error "Java version $java_version found, but Java 17+ is required"
            return 1
        fi
    else
        print_error "Java not found in PATH"
        return 1
    fi
}

test_maven() {
    print_info "Testing Maven installation..."
    
    if command -v mvn &> /dev/null; then
        local maven_version=$(mvn -version 2>&1 | head -n 1 | cut -d' ' -f3)
        print_success "Maven $maven_version found"
        return 0
    else
        print_error "Maven not found in PATH"
        return 1
    fi
}

test_docker() {
    print_info "Testing Docker installation (optional)..."
    
    if command -v docker &> /dev/null; then
        if docker info &> /dev/null; then
            print_success "Docker is running"
            return 0
        else
            print_warning "Docker is installed but not running"
            return 1
        fi
    else
        print_warning "Docker not found (optional for infrastructure)"
        return 1
    fi
}

test_postgres() {
    print_info "Testing PostgreSQL connection..."
    
    # Test local PostgreSQL
    if command -v psql &> /dev/null; then
        if psql -h localhost -U postgres -d wedding_bazaar -c "SELECT 1;" &> /dev/null; then
            print_success "PostgreSQL connection successful"
            return 0
        fi
    fi
    
    # Test Docker PostgreSQL
    if docker ps --format "table {{.Names}}" | grep -q postgres; then
        print_success "PostgreSQL Docker container is running"
        return 0
    fi
    
    print_warning "PostgreSQL not accessible (will need to be started)"
    return 1
}

test_redis() {
    print_info "Testing Redis connection (optional)..."
    
    # Test local Redis
    if command -v redis-cli &> /dev/null; then
        if redis-cli ping &> /dev/null; then
            print_success "Redis connection successful"
            return 0
        fi
    fi
    
    # Test Docker Redis
    if docker ps --format "table {{.Names}}" | grep -q redis; then
        print_success "Redis Docker container is running"
        return 0
    fi
    
    print_warning "Redis not accessible (optional but recommended)"
    return 1
}

test_ports() {
    print_info "Testing port availability..."
    
    local ports=(8761 8888 8080 8081 8082 8083 8084 8085)
    local blocked_ports=()
    
    for port in "${ports[@]}"; do
        if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
            blocked_ports+=($port)
        fi
    done
    
    if [ ${#blocked_ports[@]} -eq 0 ]; then
        print_success "All required ports are available"
        return 0
    else
        print_warning "Ports in use: ${blocked_ports[*]}"
        return 1
    fi
}

test_project_structure() {
    print_info "Testing project structure..."
    
    local required_dirs=("service-registry" "config-server" "api-gateway" "user-service" "shared-library")
    local missing_dirs=()
    
    for dir in "${required_dirs[@]}"; do
        if [ ! -d "$dir" ]; then
            missing_dirs+=($dir)
        fi
    done
    
    if [ ${#missing_dirs[@]} -eq 0 ]; then
        print_success "Project structure is complete"
        return 0
    else
        print_error "Missing directories: ${missing_dirs[*]}"
        return 1
    fi
}

# Main test execution
main() {
    print_header
    
    local tests_passed=0
    local tests_failed=0
    
    # Run tests
    if test_java; then ((tests_passed++)); else ((tests_failed++)); fi
    if test_maven; then ((tests_passed++)); else ((tests_failed++)); fi
    if test_docker; then ((tests_passed++)); else ((tests_failed++)); fi
    if test_postgres; then ((tests_passed++)); else ((tests_failed++)); fi
    if test_redis; then ((tests_passed++)); else ((tests_failed++)); fi
    if test_ports; then ((tests_passed++)); else ((tests_failed++)); fi
    if test_project_structure; then ((tests_passed++)); else ((tests_failed++)); fi
    
    echo ""
    echo "═══════════════════════════════════════════════════════════════"
    echo -e "Test Results: ${GREEN}$tests_passed passed${NC}, ${RED}$tests_failed failed${NC}"
    echo "═══════════════════════════════════════════════════════════════"
    echo ""
    
    if [ $tests_failed -eq 0 ]; then
        print_success "All tests passed! You're ready to run the application."
        echo ""
        echo "Next steps:"
        echo "1. Start infrastructure: docker-compose -f docker-compose.dev.yml up -d postgres redis"
        echo "2. Run the application: ./quick-start.sh"
        echo ""
    else
        print_warning "Some tests failed. Please address the issues above."
        echo ""
        echo "Installation help:"
        echo "- Java 17+: https://adoptium.net/"
        echo "- Maven: https://maven.apache.org/install.html"
        echo "- Docker: https://docs.docker.com/get-docker/"
        echo ""
        echo "For detailed setup instructions, see INSTALLATION_GUIDE.md"
        echo ""
    fi
    
    # Show quick setup commands
    echo "Quick Infrastructure Setup:"
    echo "─────────────────────────────"
    echo "# PostgreSQL with Docker:"
    echo "docker run -d --name postgres-wedding \\"
    echo "  -e POSTGRES_DB=wedding_bazaar \\"
    echo "  -e POSTGRES_USER=postgres \\"
    echo "  -e POSTGRES_PASSWORD=postgres \\"
    echo "  -p 5432:5432 postgres:15"
    echo ""
    echo "# Redis with Docker:"
    echo "docker run -d --name redis-wedding \\"
    echo "  -p 6379:6379 redis:7-alpine"
    echo ""
}

# Run the tests
main "$@"
