package com.weddingbazaar.vendor.entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * Service entity offered by vendors
 */
@Entity
@Table(name = "services")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EntityListeners(AuditingEntityListener.class)
public class Service {
    
    @Id
    @GeneratedValue(strategy = GenerationType.UUID)
    private String id;
    
    @Column(name = "vendor_id", nullable = false)
    private String vendorId;
    
    @Column(nullable = false)
    private String name;
    
    @Column(columnDefinition = "TEXT")
    private String description;
    
    @Column(name = "base_price", nullable = false)
    private BigDecimal basePrice;
    
    @Column(name = "max_price")
    private BigDecimal maxPrice;
    
    @Column(name = "price_unit")
    private String priceUnit; // per day, per hour, per event, etc.
    
    @Column(name = "duration_hours")
    private Integer durationHours;
    
    @Column(name = "max_guests")
    private Integer maxGuests;
    
    @Column(name = "advance_booking_days")
    private Integer advanceBookingDays = 7; // minimum days to book in advance
    
    @Column(name = "cancellation_policy")
    private String cancellationPolicy;
    
    @ElementCollection
    @CollectionTable(name = "service_features", joinColumns = @JoinColumn(name = "service_id"))
    @Column(name = "feature")
    private List<String> features;
    
    @ElementCollection
    @CollectionTable(name = "service_inclusions", joinColumns = @JoinColumn(name = "service_id"))
    @Column(name = "inclusion")
    private List<String> inclusions;
    
    @ElementCollection
    @CollectionTable(name = "service_exclusions", joinColumns = @JoinColumn(name = "service_id"))
    @Column(name = "exclusion")
    private List<String> exclusions;
    
    @ElementCollection
    @CollectionTable(name = "service_images", joinColumns = @JoinColumn(name = "service_id"))
    @Column(name = "image_url")
    private List<String> imageUrls;
    
    @Column(name = "is_active")
    private Boolean isActive = true;
    
    @Column(name = "is_featured")
    private Boolean isFeatured = false;
    
    @Column(name = "booking_count")
    private Integer bookingCount = 0;
    
    @Column(name = "rating_average")
    private BigDecimal ratingAverage = BigDecimal.ZERO;
    
    @Column(name = "review_count")
    private Integer reviewCount = 0;
    
    @CreatedDate
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;
    
    @LastModifiedDate
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
}
