@echo off
setlocal enabledelayedexpansion

REM Wedding Bazaar Backend - Windows Setup Test Script
REM This script tests if all prerequisites are properly installed

echo ================================================================
echo                Wedding Bazaar Backend
echo                   Setup Test Script
echo ================================================================
echo.

set tests_passed=0
set tests_failed=0

REM Test Java
echo [INFO] Testing Java installation...
java -version >nul 2>&1
if %errorlevel% equ 0 (
    echo [SUCCESS] Java found
    set /a tests_passed+=1
) else (
    echo [ERROR] Java not found in PATH
    set /a tests_failed+=1
)

REM Test Maven
echo [INFO] Testing Maven installation...
mvn -version >nul 2>&1
if %errorlevel% equ 0 (
    echo [SUCCESS] Maven found
    set /a tests_passed+=1
) else (
    echo [ERROR] Maven not found in PATH
    set /a tests_failed+=1
)

REM Test Docker
echo [INFO] Testing Docker installation (optional)...
docker --version >nul 2>&1
if %errorlevel% equ 0 (
    docker info >nul 2>&1
    if !errorlevel! equ 0 (
        echo [SUCCESS] Docker is running
        set /a tests_passed+=1
    ) else (
        echo [WARNING] Docker is installed but not running
        set /a tests_failed+=1
    )
) else (
    echo [WARNING] Docker not found (optional for infrastructure)
    set /a tests_failed+=1
)

REM Test project structure
echo [INFO] Testing project structure...
set missing_dirs=
if not exist "service-registry" set missing_dirs=!missing_dirs! service-registry
if not exist "config-server" set missing_dirs=!missing_dirs! config-server
if not exist "api-gateway" set missing_dirs=!missing_dirs! api-gateway
if not exist "user-service" set missing_dirs=!missing_dirs! user-service
if not exist "shared-library" set missing_dirs=!missing_dirs! shared-library

if "!missing_dirs!"=="" (
    echo [SUCCESS] Project structure is complete
    set /a tests_passed+=1
) else (
    echo [ERROR] Missing directories:!missing_dirs!
    set /a tests_failed+=1
)

REM Test ports (simplified for Windows)
echo [INFO] Testing port availability...
netstat -an | findstr ":8080 " >nul 2>&1
if %errorlevel% equ 0 (
    echo [WARNING] Port 8080 is in use
) else (
    echo [SUCCESS] Key ports appear to be available
)

echo.
echo ================================================================
echo Test Results: %tests_passed% passed, %tests_failed% failed
echo ================================================================
echo.

if %tests_failed% equ 0 (
    echo [SUCCESS] All tests passed! You're ready to run the application.
    echo.
    echo Next steps:
    echo 1. Start infrastructure services
    echo 2. Run: run-application.bat
) else (
    echo [WARNING] Some tests failed. Please address the issues above.
    echo.
    echo Installation help:
    echo - Java 17+: https://adoptium.net/
    echo - Maven: https://maven.apache.org/install.html
    echo - Docker: https://docs.docker.com/get-docker/
    echo.
    echo For detailed setup instructions, see INSTALLATION_GUIDE.md
)

echo.
echo Quick Infrastructure Setup:
echo --------------------------------
echo PostgreSQL with Docker:
echo docker run -d --name postgres-wedding ^
echo   -e POSTGRES_DB=wedding_bazaar ^
echo   -e POSTGRES_USER=postgres ^
echo   -e POSTGRES_PASSWORD=postgres ^
echo   -p 5432:5432 postgres:15
echo.
echo Redis with Docker:
echo docker run -d --name redis-wedding ^
echo   -p 6379:6379 redis:7-alpine
echo.

pause
