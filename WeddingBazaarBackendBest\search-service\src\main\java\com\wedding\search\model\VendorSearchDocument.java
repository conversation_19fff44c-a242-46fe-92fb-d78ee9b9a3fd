package com.wedding.search.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.elasticsearch.annotations.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * Enhanced Elasticsearch document for comprehensive vendor search
 */
@Document(indexName = "vendors")
@Setting(settingPath = "/elasticsearch/vendor-settings.json")
@Mapping(mappingPath = "/elasticsearch/vendor-mapping.json")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class VendorSearchDocument {
    
    @Id
    private String id;
    
    private String vendorId;
    
    @Field(type = FieldType.Text, analyzer = "standard", searchAnalyzer = "standard")
    private String businessName;
    
    @Field(type = FieldType.Text, analyzer = "standard")
    private String shortDescription;
    
    @Field(type = FieldType.Text, analyzer = "standard")
    private String description;
    
    @Field(type = FieldType.Keyword)
    private String category;
    
    @Field(type = FieldType.Keyword)
    private List<String> subcategories;
    
    @Field(type = FieldType.Keyword)
    private String city;
    
    @Field(type = FieldType.Keyword)
    private String state;
    
    @Field(type = FieldType.Keyword)
    private String country;
    
    @Field(type = FieldType.Text)
    private String pincode;
    
    @Field(type = FieldType.Text, analyzer = "standard")
    private String fullAddress;
    
    @GeoPointField
    private String location; // "lat,lon" format
    
    @Field(type = FieldType.Double)
    private BigDecimal ratingAverage;
    
    @Field(type = FieldType.Integer)
    private Integer totalReviews;
    
    @Field(type = FieldType.Integer)
    private Integer totalBookings;
    
    @Field(type = FieldType.Integer)
    private Integer completedBookings;
    
    @Field(type = FieldType.Double)
    private Double completionRate;
    
    @Field(type = FieldType.Boolean)
    private Boolean isVerified;
    
    @Field(type = FieldType.Boolean)
    private Boolean isActive;
    
    @Field(type = FieldType.Boolean)
    private Boolean isFeatured;
    
    @Field(type = FieldType.Boolean)
    private Boolean isPremium;
    
    @Field(type = FieldType.Keyword)
    private String verificationStatus;
    
    @Field(type = FieldType.Double)
    private BigDecimal startingPrice;
    
    @Field(type = FieldType.Double)
    private BigDecimal maxPrice;
    
    @Field(type = FieldType.Integer)
    private Integer yearsOfExperience;
    
    @Field(type = FieldType.Integer)
    private Integer teamSize;
    
    @Field(type = FieldType.Text, analyzer = "standard")
    private List<String> serviceAreas;
    
    @Field(type = FieldType.Text, analyzer = "standard")
    private List<String> specializations;
    
    @Field(type = FieldType.Keyword)
    private List<String> languages;
    
    @Field(type = FieldType.Integer)
    private Integer responseTimeHours;
    
    @Field(type = FieldType.Integer)
    private Integer advanceBookingDays;
    
    @Field(type = FieldType.Text)
    private String profileImageUrl;
    
    @Field(type = FieldType.Text)
    private List<String> galleryImages;
    
    @Field(type = FieldType.Text)
    private String coverImageUrl;
    
    @Field(type = FieldType.Keyword)
    private String subscriptionPlan;
    
    @Field(type = FieldType.Date)
    private LocalDateTime lastActive;
    
    @Field(type = FieldType.Date)
    private LocalDateTime createdAt;
    
    @Field(type = FieldType.Date)
    private LocalDateTime updatedAt;
    
    // Services offered by the vendor
    @Field(type = FieldType.Nested)
    private List<ServiceInfo> services;
    
    // Social media metrics
    @Field(type = FieldType.Object)
    private SocialMetrics socialMetrics;
    
    // Business hours
    @Field(type = FieldType.Object)
    private Map<String, BusinessHour> businessHours;
    
    // Completion suggester for autocomplete
    @CompletionField(maxInputLength = 100)
    private Suggest suggest;
    
    // Nested classes for complex data structures
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ServiceInfo {
        private String id;
        private String name;
        private String category;
        private String subcategory;
        private BigDecimal basePrice;
        private BigDecimal maxPrice;
        private String priceUnit;
        private Boolean isActive;
        private Boolean isFeatured;
        private List<String> features;
        private List<String> tags;
        private BigDecimal ratingAverage;
        private Integer reviewCount;
        private Integer bookingCount;
    }
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SocialMetrics {
        private Integer facebookFollowers;
        private Integer instagramFollowers;
        private Integer youtubeSubscribers;
        private Double engagementRate;
        private LocalDateTime lastUpdated;
    }
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class BusinessHour {
        private Boolean isOpen;
        private String openTime;
        private String closeTime;
        private String breakStartTime;
        private String breakEndTime;
    }
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Suggest {
        private String[] input;
        private Integer weight;
        private Map<String, Object> contexts;
    }
    
    // Helper methods for search optimization
    public void updateSuggestField() {
        this.suggest = Suggest.builder()
                .input(new String[]{
                    businessName,
                    category,
                    city + " " + category,
                    businessName + " " + city
                })
                .weight(calculateWeight())
                .build();
    }
    
    private Integer calculateWeight() {
        int weight = 1;
        
        // Boost verified vendors
        if (Boolean.TRUE.equals(isVerified)) weight += 10;
        
        // Boost featured vendors
        if (Boolean.TRUE.equals(isFeatured)) weight += 5;
        
        // Boost premium vendors
        if (Boolean.TRUE.equals(isPremium)) weight += 3;
        
        // Boost based on rating
        if (ratingAverage != null) {
            weight += ratingAverage.intValue();
        }
        
        // Boost based on completion rate
        if (completionRate != null && completionRate > 80) {
            weight += 2;
        }
        
        return weight;
    }
}
