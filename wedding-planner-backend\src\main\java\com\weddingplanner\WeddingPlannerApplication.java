package com.weddingplanner;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * Main application class for Wedding Planner Backend
 * 
 * Features enabled:
 * - JPA Auditing for automatic entity tracking
 * - Caching for performance optimization
 * - Async processing for non-blocking operations
 * - Scheduling for background tasks
 * - Transaction management
 * 
 * <AUTHOR> Planner Team
 * @version 1.0.0
 * @since 2024-01-01
 */
@SpringBootApplication
@EnableJpaAuditing
@EnableJpaRepositories(basePackages = "com.weddingplanner.repository.jpa")
@EnableCaching
@EnableAsync
@EnableScheduling
@EnableTransactionManagement
public class WeddingPlannerApplication {

    public static void main(String[] args) {
        SpringApplication.run(WeddingPlannerApplication.class, args);
    }
}
