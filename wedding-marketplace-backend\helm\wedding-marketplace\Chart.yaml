apiVersion: v2
name: wedding-marketplace
description: Advanced Helm chart for Wedding Marketplace Backend with enterprise features
type: application
version: 1.0.0
appVersion: "1.0.0"
home: https://wedding-marketplace.com
sources:
  - https://github.com/wedding-marketplace/backend
maintainers:
  - name: Wedding Marketplace Team
    email: <EMAIL>
    url: https://wedding-marketplace.com
keywords:
  - wedding
  - marketplace
  - microservices
  - spring-boot
  - kubernetes
  - enterprise
annotations:
  category: Application
  licenses: MIT
dependencies:
  - name: mysql
    version: "9.4.6"
    repository: "https://charts.bitnami.com/bitnami"
    condition: mysql.enabled
  - name: redis
    version: "18.1.5"
    repository: "https://charts.bitnami.com/bitnami"
    condition: redis.enabled
  - name: kafka
    version: "25.3.5"
    repository: "https://charts.bitnami.com/bitnami"
    condition: kafka.enabled
  - name: elasticsearch
    version: "19.13.0"
    repository: "https://charts.bitnami.com/bitnami"
    condition: elasticsearch.enabled
  - name: prometheus
    version: "25.6.0"
    repository: "https://prometheus-community.github.io/helm-charts"
    condition: monitoring.prometheus.enabled
  - name: grafana
    version: "7.0.8"
    repository: "https://grafana.github.io/helm-charts"
    condition: monitoring.grafana.enabled
  - name: jaeger
    version: "0.71.14"
    repository: "https://jaegertracing.github.io/helm-charts"
    condition: tracing.jaeger.enabled
