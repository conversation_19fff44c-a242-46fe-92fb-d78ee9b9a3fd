package com.wedding.shared.exception;

/**
 * Exception thrown when a service is temporarily unavailable
 */
public class ServiceUnavailableException extends RuntimeException {
    
    public ServiceUnavailableException(String message) {
        super(message);
    }
    
    public ServiceUnavailableException(String serviceName) {
        super(String.format("Service '%s' is currently unavailable", serviceName));
    }
    
    public ServiceUnavailableException(String message, Throwable cause) {
        super(message, cause);
    }
}
