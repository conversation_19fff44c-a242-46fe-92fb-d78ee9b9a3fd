package com.weddingbazaar.search.service;

import com.weddingbazaar.common.dto.PageResponse;
import com.weddingbazaar.common.enums.VendorCategory;
import com.weddingbazaar.search.document.VendorSearchDocument;
import com.weddingbazaar.search.dto.SearchRequest;
import com.weddingbazaar.search.dto.SearchResponse;
import com.weddingbazaar.search.dto.SuggestionResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.elasticsearch.client.elc.ElasticsearchTemplate;
import org.springframework.data.elasticsearch.core.SearchHit;
import org.springframework.data.elasticsearch.core.SearchHits;
import org.springframework.data.elasticsearch.core.query.Criteria;
import org.springframework.data.elasticsearch.core.query.CriteriaQuery;
import org.springframework.data.elasticsearch.core.query.Query;
import org.springframework.data.elasticsearch.core.suggest.Completion;
import org.springframework.data.elasticsearch.core.suggest.CompletionSuggestion;
import org.springframework.data.elasticsearch.core.suggest.Suggest;
import org.springframework.data.elasticsearch.core.suggest.SuggestBuilder;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Search service implementation using Elasticsearch
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class SearchService {
    
    private final ElasticsearchTemplate elasticsearchTemplate;
    
    public PageResponse<SearchResponse> searchVendors(SearchRequest request) {
        log.info("Searching vendors with criteria: {}", request);
        
        Criteria criteria = buildSearchCriteria(request);
        Query query = new CriteriaQuery(criteria);
        
        // Add pagination
        Pageable pageable = PageRequest.of(request.getPage(), request.getSize());
        query.setPageable(pageable);
        
        // Add sorting
        if (request.getSortBy() != null) {
            // Add sorting logic based on request.getSortBy()
        }
        
        SearchHits<VendorSearchDocument> searchHits = elasticsearchTemplate.search(query, VendorSearchDocument.class);
        
        List<SearchResponse> vendors = searchHits.getSearchHits().stream()
                .map(this::mapToSearchResponse)
                .collect(Collectors.toList());
        
        return PageResponse.of(
                vendors,
                request.getPage(),
                request.getSize(),
                searchHits.getTotalHits()
        );
    }
    
    public List<SuggestionResponse> getSuggestions(String query, int size) {
        log.info("Getting suggestions for query: {}", query);
        
        SuggestBuilder suggestBuilder = new SuggestBuilder();
        suggestBuilder.addSuggestion("vendor-suggest", 
                Completion.builder()
                        .field("suggest")
                        .prefix(query)
                        .size(size)
                        .build());
        
        Suggest suggest = elasticsearchTemplate.suggest(suggestBuilder.build(), VendorSearchDocument.class);
        
        return suggest.getSuggestion("vendor-suggest")
                .getEntries()
                .stream()
                .flatMap(entry -> ((CompletionSuggestion.Entry) entry).getOptions().stream())
                .map(option -> SuggestionResponse.builder()
                        .text(option.getText().string())
                        .score(option.getScore())
                        .build())
                .collect(Collectors.toList());
    }
    
    public PageResponse<SearchResponse> searchNearby(double latitude, double longitude, 
                                                   double radiusKm, SearchRequest request) {
        log.info("Searching nearby vendors at lat: {}, lon: {}, radius: {} km", 
                latitude, longitude, radiusKm);
        
        // Build geo-distance query
        Criteria criteria = buildSearchCriteria(request);
        
        // Add geo-distance filter
        criteria = criteria.and(Criteria.where("location")
                .within(latitude + "," + longitude, radiusKm + "km"));
        
        Query query = new CriteriaQuery(criteria);
        
        Pageable pageable = PageRequest.of(request.getPage(), request.getSize());
        query.setPageable(pageable);
        
        SearchHits<VendorSearchDocument> searchHits = elasticsearchTemplate.search(query, VendorSearchDocument.class);
        
        List<SearchResponse> vendors = searchHits.getSearchHits().stream()
                .map(this::mapToSearchResponse)
                .collect(Collectors.toList());
        
        return PageResponse.of(
                vendors,
                request.getPage(),
                request.getSize(),
                searchHits.getTotalHits()
        );
    }
    
    private Criteria buildSearchCriteria(SearchRequest request) {
        Criteria criteria = new Criteria();
        
        // Text search
        if (request.getQuery() != null && !request.getQuery().trim().isEmpty()) {
            criteria = criteria.and(
                    Criteria.where("businessName").matches(request.getQuery())
                            .or("description").matches(request.getQuery())
                            .or("services").matches(request.getQuery())
            );
        }
        
        // Category filter
        if (request.getCategory() != null) {
            criteria = criteria.and(Criteria.where("category").is(request.getCategory()));
        }
        
        // Location filters
        if (request.getCity() != null) {
            criteria = criteria.and(Criteria.where("city").is(request.getCity()));
        }
        
        if (request.getState() != null) {
            criteria = criteria.and(Criteria.where("state").is(request.getState()));
        }
        
        // Price range filter
        if (request.getMinPrice() != null) {
            criteria = criteria.and(Criteria.where("startingPrice").greaterThanEqual(request.getMinPrice()));
        }
        
        if (request.getMaxPrice() != null) {
            criteria = criteria.and(Criteria.where("maxPrice").lessThanEqual(request.getMaxPrice()));
        }
        
        // Rating filter
        if (request.getMinRating() != null) {
            criteria = criteria.and(Criteria.where("ratingAverage").greaterThanEqual(request.getMinRating()));
        }
        
        // Verification filter
        if (request.getVerifiedOnly() != null && request.getVerifiedOnly()) {
            criteria = criteria.and(Criteria.where("isVerified").is(true));
        }
        
        // Active vendors only
        criteria = criteria.and(Criteria.where("isActive").is(true));
        
        return criteria;
    }
    
    private SearchResponse mapToSearchResponse(SearchHit<VendorSearchDocument> hit) {
        VendorSearchDocument doc = hit.getContent();
        
        return SearchResponse.builder()
                .vendorId(doc.getVendorId())
                .businessName(doc.getBusinessName())
                .category(doc.getCategory())
                .description(doc.getDescription())
                .city(doc.getCity())
                .state(doc.getState())
                .ratingAverage(doc.getRatingAverage())
                .totalReviews(doc.getTotalReviews())
                .isVerified(doc.getIsVerified())
                .isFeatured(doc.getIsFeatured())
                .startingPrice(doc.getStartingPrice())
                .maxPrice(doc.getMaxPrice())
                .yearsOfExperience(doc.getYearsOfExperience())
                .profileImageUrl(doc.getProfileImageUrl())
                .galleryImages(doc.getGalleryImages())
                .services(doc.getServices())
                .paymentMethods(doc.getPaymentMethods())
                .languages(doc.getLanguages())
                .score(hit.getScore())
                .build();
    }
}
