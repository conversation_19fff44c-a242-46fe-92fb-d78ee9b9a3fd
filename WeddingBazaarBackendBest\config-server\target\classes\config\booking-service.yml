server:
  port: 8084

spring:
  application:
    name: booking-service
  datasource:
    url: *************************************************
    username: ${DB_USERNAME:postgres}
    password: ${DB_PASSWORD:postgres}
  jpa:
    hibernate:
      ddl-auto: validate
  flyway:
    locations: classpath:db/migration
    baseline-version: 1

# Booking Service Specific Configuration
booking-service:
  booking:
    auto-confirm: false
    confirmation-timeout-hours: 24
    cancellation:
      customer-deadline-hours: 48
      vendor-deadline-hours: 24
      refund-policy:
        full-refund-days: 7
        partial-refund-days: 3
        no-refund-days: 1
    payment:
      advance-percentage: 25
      full-payment-deadline-days: 7
      auto-cancel-unpaid-hours: 24
  availability:
    buffer-hours: 2
    max-advance-booking-days: 365
    min-advance-booking-hours: 24
  notifications:
    booking-confirmation: true
    payment-reminders: true
    cancellation-alerts: true
    vendor-notifications: true
  workflow:
    states:
      - PENDING
      - CONFIRMED
      - PAID
      - IN_PROGRESS
      - COMPLETED
      - CANCELLED
      - REFUNDED
    auto-transitions:
      pending-to-cancelled-hours: 24
      confirmed-to-cancelled-hours: 48

logging:
  level:
    com.wedding.booking: DEBUG
