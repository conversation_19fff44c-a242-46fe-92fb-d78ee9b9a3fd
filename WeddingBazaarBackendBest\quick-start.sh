#!/bin/bash

# Wedding Bazaar Backend - Quick Start Script
# This script provides a fast way to get the application running

set -e

# Colors
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
RED='\033[0;31m'
NC='\033[0m'

print_header() {
    echo -e "${BLUE}"
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║                    Wedding Bazaar Backend                    ║"
    echo "║                      Quick Start Guide                       ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
}

print_step() {
    echo -e "${YELLOW}➤ $1${NC}"
}

print_success() {
    echo -e "${GREEN}✓ $1${NC}"
}

print_error() {
    echo -e "${RED}✗ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ $1${NC}"
}

# Check if running on Windows (Git Bash, WSL, etc.)
is_windows() {
    [[ "$OSTYPE" == "msys" || "$OSTYPE" == "cygwin" || -n "$WSL_DISTRO_NAME" ]]
}

# Function to check prerequisites
check_prerequisites() {
    print_step "Checking prerequisites..."
    
    local missing_tools=()
    
    # Check Java
    if ! command -v java &> /dev/null; then
        missing_tools+=("Java 17+")
    else
        local java_version=$(java -version 2>&1 | head -n 1 | cut -d'"' -f2 | cut -d'.' -f1)
        if [ "$java_version" -lt 17 ]; then
            missing_tools+=("Java 17+ (current: $java_version)")
        else
            print_success "Java $java_version found"
        fi
    fi
    
    # Check Maven
    if ! command -v mvn &> /dev/null; then
        missing_tools+=("Maven")
    else
        print_success "Maven found"
    fi
    
    # Check Docker (optional but recommended)
    if ! command -v docker &> /dev/null; then
        print_info "Docker not found (optional for infrastructure)"
    else
        print_success "Docker found"
    fi
    
    if [ ${#missing_tools[@]} -ne 0 ]; then
        print_error "Missing required tools:"
        for tool in "${missing_tools[@]}"; do
            echo "  - $tool"
        done
        echo ""
        echo "Please install the missing tools and try again."
        exit 1
    fi
}

# Function to start infrastructure with Docker
start_infrastructure_docker() {
    print_step "Starting infrastructure services with Docker..."
    
    if ! command -v docker-compose &> /dev/null && ! command -v docker &> /dev/null; then
        print_error "Docker Compose not found"
        return 1
    fi
    
    # Start only essential infrastructure
    if is_windows; then
        winpty docker-compose -f docker-compose.dev.yml up -d postgres redis
    else
        docker-compose -f docker-compose.dev.yml up -d postgres redis
    fi
    
    print_info "Waiting for infrastructure to be ready..."
    sleep 15
    
    print_success "Infrastructure services started"
    return 0
}

# Function to start infrastructure manually
start_infrastructure_manual() {
    print_step "Manual infrastructure setup required"
    
    echo ""
    echo "Please ensure the following services are running:"
    echo "  1. PostgreSQL on port 5432"
    echo "     - Database: wedding_bazaar"
    echo "     - Username: postgres"
    echo "     - Password: postgres"
    echo ""
    echo "  2. Redis on port 6379 (optional but recommended)"
    echo ""
    echo "Quick PostgreSQL setup with Docker:"
    echo "  docker run -d --name postgres-wedding \\"
    echo "    -e POSTGRES_DB=wedding_bazaar \\"
    echo "    -e POSTGRES_USER=postgres \\"
    echo "    -e POSTGRES_PASSWORD=postgres \\"
    echo "    -p 5432:5432 postgres:15"
    echo ""
    
    read -p "Press Enter when infrastructure is ready..."
}

# Function to build the project
build_project() {
    print_step "Building the project..."
    
    # Build only essential modules first
    mvn clean install -DskipTests -pl shared-library,service-registry,config-server,api-gateway,user-service -am
    
    if [ $? -eq 0 ]; then
        print_success "Core modules built successfully"
    else
        print_error "Build failed"
        exit 1
    fi
}

# Function to start core services
start_core_services() {
    print_step "Starting core services..."
    
    # Create logs directory
    mkdir -p logs
    
    # Start services in order with minimal wait
    print_info "Starting Service Registry..."
    cd service-registry
    nohup mvn spring-boot:run -Dspring.profiles.active=dev > ../logs/service-registry.log 2>&1 &
    echo $! > ../logs/service-registry.pid
    cd ..
    sleep 10
    
    print_info "Starting Config Server..."
    cd config-server
    nohup mvn spring-boot:run -Dspring.profiles.active=dev > ../logs/config-server.log 2>&1 &
    echo $! > ../logs/config-server.pid
    cd ..
    sleep 8
    
    print_info "Starting API Gateway..."
    cd api-gateway
    nohup mvn spring-boot:run -Dspring.profiles.active=dev > ../logs/api-gateway.log 2>&1 &
    echo $! > ../logs/api-gateway.pid
    cd ..
    sleep 8
    
    print_info "Starting User Service..."
    cd user-service
    nohup mvn spring-boot:run -Dspring.profiles.active=dev > ../logs/user-service.log 2>&1 &
    echo $! > ../logs/user-service.pid
    cd ..
    sleep 5
    
    print_success "Core services started"
}

# Function to check service health
check_services() {
    print_step "Checking service health..."
    
    local services=(
        "Service Registry:8761"
        "Config Server:8888"
        "API Gateway:8080"
        "User Service:8081"
    )
    
    local all_healthy=true
    
    for service_info in "${services[@]}"; do
        local service_name=$(echo $service_info | cut -d':' -f1)
        local port=$(echo $service_info | cut -d':' -f2)
        
        print_info "Checking $service_name..."
        
        local attempts=0
        local max_attempts=15
        
        while [ $attempts -lt $max_attempts ]; do
            if curl -s -f "http://localhost:$port/actuator/health" > /dev/null 2>&1; then
                print_success "$service_name is healthy"
                break
            fi
            
            attempts=$((attempts + 1))
            sleep 2
        done
        
        if [ $attempts -eq $max_attempts ]; then
            print_error "$service_name is not responding"
            all_healthy=false
        fi
    done
    
    return $all_healthy
}

# Function to show final status
show_final_status() {
    echo ""
    echo -e "${GREEN}🎉 Wedding Bazaar Backend is running!${NC}"
    echo ""
    echo "Available endpoints:"
    echo "  • API Gateway: http://localhost:8080"
    echo "  • Swagger UI: http://localhost:8080/swagger-ui.html"
    echo "  • Eureka Dashboard: http://localhost:8761"
    echo "  • Config Server: http://localhost:8888"
    echo ""
    echo "Service logs are available in the 'logs/' directory"
    echo ""
    echo "To stop all services, run:"
    echo "  ./quick-start.sh stop"
    echo ""
}

# Function to stop services
stop_services() {
    print_step "Stopping all services..."
    
    # Kill services using PID files
    for pid_file in logs/*.pid; do
        if [ -f "$pid_file" ]; then
            local pid=$(cat "$pid_file")
            local service_name=$(basename "$pid_file" .pid)
            
            if kill -0 "$pid" 2>/dev/null; then
                print_info "Stopping $service_name (PID: $pid)"
                kill "$pid"
                rm -f "$pid_file"
            fi
        fi
    done
    
    # Stop Docker infrastructure if running
    if command -v docker-compose &> /dev/null; then
        docker-compose -f docker-compose.dev.yml down
    fi
    
    print_success "All services stopped"
}

# Main execution
main() {
    local command=${1:-start}
    
    case $command in
        start)
            print_header
            check_prerequisites
            
            # Try Docker first, fallback to manual
            if ! start_infrastructure_docker; then
                start_infrastructure_manual
            fi
            
            build_project
            start_core_services
            
            if check_services; then
                show_final_status
            else
                print_error "Some services failed to start. Check logs in 'logs/' directory"
                exit 1
            fi
            ;;
        stop)
            stop_services
            ;;
        status)
            check_services
            ;;
        *)
            echo "Usage: $0 [start|stop|status]"
            echo ""
            echo "Commands:"
            echo "  start   - Start the application (default)"
            echo "  stop    - Stop all services"
            echo "  status  - Check service status"
            ;;
    esac
}

# Handle Ctrl+C
trap 'echo ""; print_info "Use ./quick-start.sh stop to stop services"; exit 0' INT

# Run main function
main "$@"
