version: '3.8'

services:
  # Wedding Marketplace Backend API
  wedding-marketplace-api:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: wedding-marketplace-api
    ports:
      - "8080:8080"
    environment:
      - SPRING_PROFILES_ACTIVE=docker
      - SPRING_DATASOURCE_URL=********************************************************************************************************
      - SPRING_DATASOURCE_USERNAME=wedding_user
      - SPRING_DATASOURCE_PASSWORD=wedding_password
      - SPRING_REDIS_HOST=redis
      - SPRING_REDIS_PORT=6379
      - SPRING_ELASTICSEARCH_URIS=http://elasticsearch:9200
      - AWS_ACCESS_KEY_ID=${AWS_ACCESS_KEY_ID}
      - AWS_SECRET_ACCESS_KEY=${AWS_SECRET_ACCESS_KEY}
      - AWS_REGION=${AWS_REGION:-us-east-1}
      - STRIPE_SECRET_KEY=${STRIPE_SECRET_KEY}
      - RAZORPAY_KEY_ID=${RAZORPAY_KEY_ID}
      - RAZORPAY_KEY_SECRET=${RAZORPAY_KEY_SECRET}
      - JWT_SECRET=${JWT_SECRET:-wedding-marketplace-jwt-secret-key-2024}
      - MAIL_USERNAME=${MAIL_USERNAME}
      - MAIL_PASSWORD=${MAIL_PASSWORD}
    depends_on:
      mysql:
        condition: service_healthy
      redis:
        condition: service_healthy
      elasticsearch:
        condition: service_healthy
    networks:
      - wedding-marketplace-network
    volumes:
      - ./logs:/app/logs
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # MySQL Database
  mysql:
    image: mysql:8.0
    container_name: wedding-marketplace-mysql
    ports:
      - "3306:3306"
    environment:
      - MYSQL_ROOT_PASSWORD=root_password
      - MYSQL_DATABASE=wedding_marketplace
      - MYSQL_USER=wedding_user
      - MYSQL_PASSWORD=wedding_password
    volumes:
      - mysql_data:/var/lib/mysql
      - ./docker/mysql/init:/docker-entrypoint-initdb.d
      - ./docker/mysql/conf:/etc/mysql/conf.d
    networks:
      - wedding-marketplace-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "wedding_user", "-pwedding_password"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    command: --default-authentication-plugin=mysql_native_password

  # Redis Cache
  redis:
    image: redis:7.2-alpine
    container_name: wedding-marketplace-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
      - ./docker/redis/redis.conf:/usr/local/etc/redis/redis.conf
    networks:
      - wedding-marketplace-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    command: redis-server /usr/local/etc/redis/redis.conf

  # Elasticsearch
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.11.0
    container_name: wedding-marketplace-elasticsearch
    ports:
      - "9200:9200"
      - "9300:9300"
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    networks:
      - wedding-marketplace-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:9200/_cluster/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # Kibana (Optional - for Elasticsearch visualization)
  kibana:
    image: docker.elastic.co/kibana/kibana:8.11.0
    container_name: wedding-marketplace-kibana
    ports:
      - "5601:5601"
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
      - xpack.security.enabled=false
    depends_on:
      elasticsearch:
        condition: service_healthy
    networks:
      - wedding-marketplace-network
    restart: unless-stopped
    profiles:
      - monitoring

  # Prometheus (Optional - for metrics collection)
  prometheus:
    image: prom/prometheus:latest
    container_name: wedding-marketplace-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./docker/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    networks:
      - wedding-marketplace-network
    restart: unless-stopped
    profiles:
      - monitoring

  # Grafana (Optional - for metrics visualization)
  grafana:
    image: grafana/grafana:latest
    container_name: wedding-marketplace-grafana
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
    volumes:
      - grafana_data:/var/lib/grafana
      - ./docker/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./docker/grafana/datasources:/etc/grafana/provisioning/datasources
    depends_on:
      - prometheus
    networks:
      - wedding-marketplace-network
    restart: unless-stopped
    profiles:
      - monitoring

  # Nginx (Optional - for load balancing and reverse proxy)
  nginx:
    image: nginx:alpine
    container_name: wedding-marketplace-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./docker/nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./docker/nginx/ssl:/etc/nginx/ssl
    depends_on:
      - wedding-marketplace-api
    networks:
      - wedding-marketplace-network
    restart: unless-stopped
    profiles:
      - production

  # MinIO (Optional - for local S3-compatible storage)
  minio:
    image: minio/minio:latest
    container_name: wedding-marketplace-minio
    ports:
      - "9000:9000"
      - "9001:9001"
    environment:
      - MINIO_ROOT_USER=minioadmin
      - MINIO_ROOT_PASSWORD=minioadmin123
    volumes:
      - minio_data:/data
    networks:
      - wedding-marketplace-network
    restart: unless-stopped
    profiles:
      - development
    command: server /data --console-address ":9001"

networks:
  wedding-marketplace-network:
    driver: bridge
    name: wedding-marketplace-network

volumes:
  mysql_data:
    driver: local
  redis_data:
    driver: local
  elasticsearch_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
  minio_data:
    driver: local
