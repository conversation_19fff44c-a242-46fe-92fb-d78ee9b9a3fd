package com.wedding.gateway.filter;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.gateway.filter.GatewayFilter;
import org.springframework.cloud.gateway.filter.factory.AbstractGatewayFilterFactory;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

import com.auth0.jwt.JWT;
import com.auth0.jwt.JWTVerifier;
import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.exceptions.JWTVerificationException;
import com.auth0.jwt.interfaces.DecodedJWT;

/**
 * JWT Authentication Filter for API Gateway
 */
@Component
@Slf4j
public class JwtAuthenticationFilter extends AbstractGatewayFilterFactory<JwtAuthenticationFilter.Config> {
    
    @Value("${jwt.secret:wedding-bazaar-secret-key-2024}")
    private String jwtSecret;
    
    @Value("${jwt.issuer:wedding-bazaar}")
    private String issuer;
    
    public JwtAuthenticationFilter() {
        super(Config.class);
    }
    
    @Override
    public GatewayFilter apply(Config config) {
        return (exchange, chain) -> {
            ServerHttpRequest request = exchange.getRequest();
            
            // Extract JWT token from Authorization header
            String token = extractToken(request);
            
            if (token == null) {
                return onError(exchange, "Missing or invalid Authorization header", HttpStatus.UNAUTHORIZED);
            }
            
            try {
                // Validate JWT token
                DecodedJWT decodedJWT = validateToken(token);
                
                // Add user information to request headers for downstream services
                ServerHttpRequest modifiedRequest = request.mutate()
                        .header("X-User-Id", decodedJWT.getSubject())
                        .header("X-User-Email", decodedJWT.getClaim("email").asString())
                        .header("X-User-Authorities", decodedJWT.getClaim("authorities").asString())
                        .header("X-User-Verified", decodedJWT.getClaim("verified").asString())
                        .build();
                
                ServerWebExchange modifiedExchange = exchange.mutate()
                        .request(modifiedRequest)
                        .build();
                
                log.debug("JWT validation successful for user: {}", decodedJWT.getSubject());
                return chain.filter(modifiedExchange);
                
            } catch (JWTVerificationException e) {
                log.warn("JWT validation failed: {}", e.getMessage());
                return onError(exchange, "Invalid or expired token", HttpStatus.UNAUTHORIZED);
            } catch (Exception e) {
                log.error("Unexpected error during JWT validation", e);
                return onError(exchange, "Authentication error", HttpStatus.INTERNAL_SERVER_ERROR);
            }
        };
    }
    
    private String extractToken(ServerHttpRequest request) {
        String authHeader = request.getHeaders().getFirst(HttpHeaders.AUTHORIZATION);
        
        if (StringUtils.hasText(authHeader) && authHeader.startsWith("Bearer ")) {
            return authHeader.substring(7);
        }
        
        return null;
    }
    
    private DecodedJWT validateToken(String token) throws JWTVerificationException {
        Algorithm algorithm = Algorithm.HMAC256(jwtSecret);
        
        JWTVerifier verifier = JWT.require(algorithm)
                .withIssuer(issuer)
                .build();
        
        return verifier.verify(token);
    }
    
    private Mono<Void> onError(ServerWebExchange exchange, String message, HttpStatus status) {
        ServerHttpResponse response = exchange.getResponse();
        response.setStatusCode(status);
        response.getHeaders().add("Content-Type", "application/json");
        
        String errorResponse = String.format(
                "{\"success\":false,\"message\":\"%s\",\"timestamp\":\"%s\"}",
                message,
                java.time.LocalDateTime.now()
        );
        
        org.springframework.core.io.buffer.DataBuffer buffer = response.bufferFactory()
                .wrap(errorResponse.getBytes());
        
        return response.writeWith(Mono.just(buffer));
    }
    
    public static class Config {
        // Configuration properties can be added here if needed
    }
}
