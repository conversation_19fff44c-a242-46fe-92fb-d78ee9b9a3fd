package com.weddingbazaar.user;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.netflix.eureka.EnableEurekaClient;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import org.springframework.kafka.annotation.EnableKafka;

/**
 * User Service Application
 */
@SpringBootApplication(scanBasePackages = {"com.weddingbazaar.user", "com.weddingbazaar.common"})
@EnableEurekaClient
@EnableJpaAuditing
@EnableKafka
public class UserServiceApplication {
    
    public static void main(String[] args) {
        SpringApplication.run(UserServiceApplication.class, args);
    }
}
