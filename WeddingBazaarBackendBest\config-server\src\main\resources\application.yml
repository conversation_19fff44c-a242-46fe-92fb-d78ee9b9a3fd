server:
  port: 8888

spring:
  application:
    name: config-server
  cloud:
    config:
      server:
        git:
          uri: https://github.com/your-org/wedding-bazaar-config
          clone-on-start: true
          default-label: main
        # For local file system (development)
        native:
          search-locations: classpath:/config
  profiles:
    active: native
  security:
    user:
      name: config
      password: config123

eureka:
  client:
    service-url:
      defaultZone: ************************************/eureka/
  instance:
    prefer-ip-address: true

management:
  endpoints:
    web:
      exposure:
        include: health,info,refresh,env
  endpoint:
    health:
      show-details: always

logging:
  level:
    org.springframework.cloud.config: DEBUG
