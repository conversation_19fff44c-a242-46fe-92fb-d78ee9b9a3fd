package com.wedding.gateway.filter;

import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.gateway.filter.GatewayFilter;
import org.springframework.cloud.gateway.filter.factory.AbstractGatewayFilterFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

import java.util.Arrays;
import java.util.List;

/**
 * Admin Authorization Filter for API Gateway
 * Ensures only users with admin roles can access admin endpoints
 */
@Component
@Slf4j
public class AdminAuthorizationFilter extends AbstractGatewayFilterFactory<AdminAuthorizationFilter.Config> {
    
    private static final List<String> ADMIN_ROLES = Arrays.asList(
            "ROLE_ADMIN", 
            "ROLE_SUPER_ADMIN"
    );
    
    public AdminAuthorizationFilter() {
        super(Config.class);
    }
    
    @Override
    public GatewayFilter apply(Config config) {
        return (exchange, chain) -> {
            ServerHttpRequest request = exchange.getRequest();
            
            // Get user authorities from headers (set by JwtAuthenticationFilter)
            String userAuthorities = request.getHeaders().getFirst("X-User-Authorities");
            String userId = request.getHeaders().getFirst("X-User-Id");
            
            if (userAuthorities == null || userId == null) {
                log.warn("Missing user information in request headers for admin endpoint");
                return onError(exchange, "Access denied: Authentication required", HttpStatus.UNAUTHORIZED);
            }
            
            // Check if user has admin role
            boolean hasAdminRole = ADMIN_ROLES.stream()
                    .anyMatch(role -> userAuthorities.contains(role));
            
            if (!hasAdminRole) {
                log.warn("User {} attempted to access admin endpoint without proper authorization. Authorities: {}", 
                        userId, userAuthorities);
                return onError(exchange, "Access denied: Admin privileges required", HttpStatus.FORBIDDEN);
            }
            
            log.debug("Admin authorization successful for user: {}", userId);
            return chain.filter(exchange);
        };
    }
    
    private Mono<Void> onError(ServerWebExchange exchange, String message, HttpStatus status) {
        ServerHttpResponse response = exchange.getResponse();
        response.setStatusCode(status);
        response.getHeaders().add("Content-Type", "application/json");
        
        String errorResponse = String.format(
                "{\"success\":false,\"message\":\"%s\",\"errorCode\":\"%s\",\"timestamp\":\"%s\"}",
                message,
                status.name(),
                java.time.LocalDateTime.now()
        );
        
        org.springframework.core.io.buffer.DataBuffer buffer = response.bufferFactory()
                .wrap(errorResponse.getBytes());
        
        return response.writeWith(Mono.just(buffer));
    }
    
    public static class Config {
        // Configuration properties can be added here if needed
    }
}
