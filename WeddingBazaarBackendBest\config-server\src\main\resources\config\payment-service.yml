server:
  port: 8085

spring:
  application:
    name: payment-service
  datasource:
    url: *************************************************
    username: ${DB_USERNAME:postgres}
    password: ${DB_PASSWORD:postgres}
  jpa:
    hibernate:
      ddl-auto: validate
  flyway:
    locations: classpath:db/migration
    baseline-version: 1

# Payment Service Specific Configuration
payment-service:
  stripe:
    public-key: ${payment.stripe.public-key}
    secret-key: ${payment.stripe.secret-key}
    webhook-secret: ${payment.stripe.webhook-secret}
    currency: INR
    capture-method: automatic
    confirmation-method: automatic
  paypal:
    client-id: ${payment.paypal.client-id}
    client-secret: ${payment.paypal.client-secret}
    mode: ${payment.paypal.mode}
    currency: INR
  razorpay:
    key-id: ${RAZORPAY_KEY_ID:your-razorpay-key-id}
    key-secret: ${RAZORPAY_KEY_SECRET:your-razorpay-key-secret}
    webhook-secret: ${RAZORPAY_WEBHOOK_SECRET:your-webhook-secret}
    currency: INR
  payment:
    retry-attempts: 3
    timeout-seconds: 300
    webhook-retry-attempts: 5
    refund:
      auto-process: false
      admin-approval-required: true
      processing-time-days: 7
    commission:
      platform-fee-percentage: 2.5
      payment-gateway-fee-percentage: 2.9
      gst-percentage: 18.0

logging:
  level:
    com.wedding.payment: DEBUG
    com.stripe: INFO
