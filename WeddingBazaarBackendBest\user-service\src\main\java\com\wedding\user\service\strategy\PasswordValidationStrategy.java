package com.wedding.user.service.strategy;

import com.wedding.shared.exception.ValidationException;
import com.wedding.user.model.User;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;
import java.util.regex.Pattern;

/**
 * Strategy pattern for password validation with multiple validation rules
 */
public interface PasswordValidationStrategy {
    
    void validate(PasswordValidationContext context);
    
    int getOrder();
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class PasswordValidationContext {
        private String password;
        private User user;
        private List<String> previousPasswords;
        private LocalDateTime lastPasswordChange;
    }
}

/**
 * Validates password length requirements
 */
@org.springframework.stereotype.Component
@org.springframework.core.annotation.Order(1)
class PasswordLengthValidationStrategy implements PasswordValidationStrategy {
    
    private static final int MIN_LENGTH = 8;
    private static final int MAX_LENGTH = 128;
    
    @Override
    public void validate(PasswordValidationContext context) {
        String password = context.getPassword();
        
        if (password == null || password.length() < MIN_LENGTH) {
            throw new ValidationException("Password must be at least " + MIN_LENGTH + " characters long");
        }
        
        if (password.length() > MAX_LENGTH) {
            throw new ValidationException("Password must not exceed " + MAX_LENGTH + " characters");
        }
    }
    
    @Override
    public int getOrder() {
        return 1;
    }
}

/**
 * Validates password complexity requirements
 */
@org.springframework.stereotype.Component
@org.springframework.core.annotation.Order(2)
class PasswordComplexityValidationStrategy implements PasswordValidationStrategy {
    
    private static final Pattern UPPERCASE_PATTERN = Pattern.compile("[A-Z]");
    private static final Pattern LOWERCASE_PATTERN = Pattern.compile("[a-z]");
    private static final Pattern DIGIT_PATTERN = Pattern.compile("[0-9]");
    private static final Pattern SPECIAL_CHAR_PATTERN = Pattern.compile("[!@#$%^&*()_+\\-=\\[\\]{};':\"\\\\|,.<>\\/?]");
    
    @Override
    public void validate(PasswordValidationContext context) {
        String password = context.getPassword();
        
        if (!UPPERCASE_PATTERN.matcher(password).find()) {
            throw new ValidationException("Password must contain at least one uppercase letter");
        }
        
        if (!LOWERCASE_PATTERN.matcher(password).find()) {
            throw new ValidationException("Password must contain at least one lowercase letter");
        }
        
        if (!DIGIT_PATTERN.matcher(password).find()) {
            throw new ValidationException("Password must contain at least one digit");
        }
        
        if (!SPECIAL_CHAR_PATTERN.matcher(password).find()) {
            throw new ValidationException("Password must contain at least one special character");
        }
    }
    
    @Override
    public int getOrder() {
        return 2;
    }
}

/**
 * Validates against common weak passwords
 */
@org.springframework.stereotype.Component
@org.springframework.core.annotation.Order(3)
class CommonPasswordValidationStrategy implements PasswordValidationStrategy {
    
    private static final List<String> COMMON_PASSWORDS = List.of(
        "password", "123456", "123456789", "12345678", "12345", "1234567",
        "password123", "admin", "qwerty", "abc123", "letmein", "welcome",
        "monkey", "dragon", "master", "shadow", "superman", "michael",
        "football", "baseball", "liverpool", "jordan", "princess"
    );
    
    @Override
    public void validate(PasswordValidationContext context) {
        String password = context.getPassword().toLowerCase();
        
        if (COMMON_PASSWORDS.contains(password)) {
            throw new ValidationException("Password is too common. Please choose a more secure password");
        }
        
        // Check for keyboard patterns
        if (isKeyboardPattern(password)) {
            throw new ValidationException("Password contains keyboard patterns. Please choose a more secure password");
        }
        
        // Check for repeated characters
        if (hasRepeatedCharacters(password)) {
            throw new ValidationException("Password contains too many repeated characters");
        }
    }
    
    private boolean isKeyboardPattern(String password) {
        String[] patterns = {"qwerty", "asdf", "zxcv", "1234", "abcd"};
        return java.util.Arrays.stream(patterns).anyMatch(password::contains);
    }
    
    private boolean hasRepeatedCharacters(String password) {
        int maxRepeats = 3;
        for (int i = 0; i <= password.length() - maxRepeats; i++) {
            char c = password.charAt(i);
            boolean allSame = true;
            for (int j = i + 1; j < i + maxRepeats; j++) {
                if (password.charAt(j) != c) {
                    allSame = false;
                    break;
                }
            }
            if (allSame) {
                return true;
            }
        }
        return false;
    }
    
    @Override
    public int getOrder() {
        return 3;
    }
}

/**
 * Validates against user's personal information
 */
@org.springframework.stereotype.Component
@org.springframework.core.annotation.Order(4)
class PersonalInfoValidationStrategy implements PasswordValidationStrategy {
    
    @Override
    public void validate(PasswordValidationContext context) {
        String password = context.getPassword().toLowerCase();
        User user = context.getUser();
        
        if (user == null) {
            return; // Skip validation if user context is not available
        }
        
        // Check against user's personal information
        if (containsPersonalInfo(password, user.getFirstName()) ||
            containsPersonalInfo(password, user.getLastName()) ||
            containsPersonalInfo(password, user.getEmail().split("@")[0])) {
            throw new ValidationException("Password should not contain personal information");
        }
        
        // Check against phone number
        if (user.getPhoneNumber() != null && password.contains(user.getPhoneNumber().replaceAll("\\D", ""))) {
            throw new ValidationException("Password should not contain phone number");
        }
        
        // Check against birth year
        if (user.getDateOfBirth() != null) {
            String birthYear = String.valueOf(user.getDateOfBirth().getYear());
            if (password.contains(birthYear)) {
                throw new ValidationException("Password should not contain birth year");
            }
        }
    }
    
    private boolean containsPersonalInfo(String password, String personalInfo) {
        if (personalInfo == null || personalInfo.length() < 3) {
            return false;
        }
        return password.contains(personalInfo.toLowerCase());
    }
    
    @Override
    public int getOrder() {
        return 4;
    }
}

/**
 * Validates against password history
 */
@org.springframework.stereotype.Component
@org.springframework.core.annotation.Order(5)
class PasswordHistoryValidationStrategy implements PasswordValidationStrategy {
    
    private static final int PASSWORD_HISTORY_COUNT = 5;
    
    @Override
    public void validate(PasswordValidationContext context) {
        List<String> previousPasswords = context.getPreviousPasswords();
        
        if (previousPasswords == null || previousPasswords.isEmpty()) {
            return; // Skip validation if no password history
        }
        
        String newPassword = context.getPassword();
        
        // Check against recent passwords (this would typically use hashed passwords)
        if (previousPasswords.stream().anyMatch(oldPassword -> isSimilarPassword(newPassword, oldPassword))) {
            throw new ValidationException("Password is too similar to a recently used password");
        }
    }
    
    private boolean isSimilarPassword(String newPassword, String oldPassword) {
        // Simple similarity check - in production, use more sophisticated algorithms
        if (newPassword.equals(oldPassword)) {
            return true;
        }
        
        // Check for minor variations (adding numbers, changing case, etc.)
        String newLower = newPassword.toLowerCase();
        String oldLower = oldPassword.toLowerCase();
        
        // Remove common suffixes/prefixes
        String[] commonSuffixes = {"1", "2", "3", "!", "@", "#", "123"};
        for (String suffix : commonSuffixes) {
            if (newLower.endsWith(suffix) && oldLower.equals(newLower.substring(0, newLower.length() - suffix.length()))) {
                return true;
            }
            if (oldLower.endsWith(suffix) && newLower.equals(oldLower.substring(0, oldLower.length() - suffix.length()))) {
                return true;
            }
        }
        
        return false;
    }
    
    @Override
    public int getOrder() {
        return 5;
    }
}

/**
 * Validates password entropy and randomness
 */
@org.springframework.stereotype.Component
@org.springframework.core.annotation.Order(6)
class PasswordEntropyValidationStrategy implements PasswordValidationStrategy {
    
    private static final double MIN_ENTROPY = 50.0; // bits
    
    @Override
    public void validate(PasswordValidationContext context) {
        String password = context.getPassword();
        double entropy = calculateEntropy(password);
        
        if (entropy < MIN_ENTROPY) {
            throw new ValidationException("Password is not complex enough. Please use a more random combination of characters");
        }
    }
    
    private double calculateEntropy(String password) {
        if (password == null || password.isEmpty()) {
            return 0.0;
        }
        
        // Calculate character set size
        int charsetSize = 0;
        boolean hasLower = false, hasUpper = false, hasDigit = false, hasSpecial = false;
        
        for (char c : password.toCharArray()) {
            if (Character.isLowerCase(c)) hasLower = true;
            else if (Character.isUpperCase(c)) hasUpper = true;
            else if (Character.isDigit(c)) hasDigit = true;
            else hasSpecial = true;
        }
        
        if (hasLower) charsetSize += 26;
        if (hasUpper) charsetSize += 26;
        if (hasDigit) charsetSize += 10;
        if (hasSpecial) charsetSize += 32; // approximate
        
        // Calculate entropy: log2(charsetSize^length)
        return password.length() * Math.log(charsetSize) / Math.log(2);
    }
    
    @Override
    public int getOrder() {
        return 6;
    }
}
