# Wedding Bazaar Backend - Installation Guide

## 📋 Prerequisites Installation

### 1. Install Java 17 or Higher

#### Windows:
1. Download OpenJDK 17 from: https://adoptium.net/
2. Run the installer and follow the setup wizard
3. Add Java to PATH:
   - Open System Properties → Advanced → Environment Variables
   - Add `JAVA_HOME` pointing to Java installation directory
   - Add `%JAVA_HOME%\bin` to PATH variable

#### Mac:
```bash
# Using Homebrew
brew install openjdk@17

# Add to PATH in ~/.zshrc or ~/.bash_profile
export JAVA_HOME=$(/usr/libexec/java_home -v 17)
export PATH=$JAVA_HOME/bin:$PATH
```

#### Linux:
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install openjdk-17-jdk

# CentOS/RHEL
sudo yum install java-17-openjdk-devel
```

**Verify Installation:**
```bash
java -version
# Should show Java 17.x.x
```

### 2. Install Maven 3.8+

#### Windows:
1. Download Maven from: https://maven.apache.org/download.cgi
2. Extract to a directory (e.g., `C:\Program Files\Apache\maven`)
3. Add Maven to PATH:
   - Add `MAVEN_HOME` pointing to Maven directory
   - Add `%MAVEN_HOME%\bin` to PATH variable

#### Mac:
```bash
# Using Homebrew
brew install maven
```

#### Linux:
```bash
# Ubuntu/Debian
sudo apt install maven

# CentOS/RHEL
sudo yum install maven
```

**Verify Installation:**
```bash
mvn -version
# Should show Maven 3.8.x or higher
```

### 3. Install PostgreSQL

#### Windows:
1. Download from: https://www.postgresql.org/download/windows/
2. Run installer and set password for `postgres` user
3. Default port: 5432

#### Mac:
```bash
# Using Homebrew
brew install postgresql
brew services start postgresql

# Create database
createdb wedding_bazaar
```

#### Linux:
```bash
# Ubuntu/Debian
sudo apt install postgresql postgresql-contrib

# Start service
sudo systemctl start postgresql
sudo systemctl enable postgresql

# Create database
sudo -u postgres createdb wedding_bazaar
```

#### Using Docker (Recommended):
```bash
docker run -d --name postgres-wedding \
  -e POSTGRES_DB=wedding_bazaar \
  -e POSTGRES_USER=postgres \
  -e POSTGRES_PASSWORD=postgres \
  -p 5432:5432 \
  postgres:15
```

### 4. Install Redis (Optional but Recommended)

#### Using Docker:
```bash
docker run -d --name redis-wedding \
  -p 6379:6379 \
  redis:7-alpine
```

#### Windows:
1. Download Redis for Windows from GitHub releases
2. Extract and run `redis-server.exe`

#### Mac:
```bash
brew install redis
brew services start redis
```

#### Linux:
```bash
# Ubuntu/Debian
sudo apt install redis-server

# Start service
sudo systemctl start redis-server
sudo systemctl enable redis-server
```

## 🚀 Running the Application

### Step 1: Clone and Navigate
```bash
cd WeddingBazaarBackendBest
```

### Step 2: Verify Prerequisites
```bash
java -version    # Should show Java 17+
mvn -version     # Should show Maven 3.8+
```

### Step 3: Start Infrastructure Services

**Option A: Using Docker (Recommended)**
```bash
# Start PostgreSQL
docker run -d --name postgres-wedding \
  -e POSTGRES_DB=wedding_bazaar \
  -e POSTGRES_USER=postgres \
  -e POSTGRES_PASSWORD=postgres \
  -p 5432:5432 postgres:15

# Start Redis
docker run -d --name redis-wedding \
  -p 6379:6379 redis:7-alpine

# Verify services are running
docker ps
```

**Option B: Using Docker Compose**
```bash
docker-compose -f docker-compose.dev.yml up -d postgres redis
```

### Step 4: Build the Project
```bash
mvn clean install -DskipTests
```

### Step 5: Start Services in Order

**Terminal 1 - Service Registry:**
```bash
cd service-registry
mvn spring-boot:run -Dspring.profiles.active=dev
```
Wait for "Started ServiceRegistryApplication" message.

**Terminal 2 - Config Server:**
```bash
cd config-server
mvn spring-boot:run -Dspring.profiles.active=dev
```
Wait for "Started ConfigServerApplication" message.

**Terminal 3 - API Gateway:**
```bash
cd api-gateway
mvn spring-boot:run -Dspring.profiles.active=dev
```
Wait for "Started ApiGatewayApplication" message.

**Terminal 4 - User Service:**
```bash
cd user-service
mvn spring-boot:run -Dspring.profiles.active=dev
```
Wait for "Started UserServiceApplication" message.

### Step 6: Verify Services

**Check Service Registry:**
Open: http://localhost:8761

**Check API Gateway:**
```bash
curl http://localhost:8080/actuator/health
```

**Check Swagger UI:**
Open: http://localhost:8080/swagger-ui.html

## 🧪 Test the Application

### 1. Register a New User
```bash
curl -X POST http://localhost:8080/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "Test123!@#",
    "firstName": "John",
    "lastName": "Doe",
    "phoneNumber": "+**********"
  }'
```

### 2. Login
```bash
curl -X POST http://localhost:8080/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "Test123!@#"
  }'
```

### 3. Check User Profile (with JWT token from login)
```bash
curl -X GET http://localhost:8080/api/users/profile \
  -H "Authorization: Bearer YOUR_JWT_TOKEN_HERE"
```

## 🐛 Troubleshooting

### Common Issues:

**1. "Command not found" errors:**
- Ensure Java and Maven are properly installed and in PATH
- Restart terminal after installation
- On Windows, use Command Prompt or PowerShell as Administrator

**2. Port already in use:**
```bash
# Windows
netstat -ano | findstr :8080
taskkill /PID <PID> /F

# Mac/Linux
lsof -ti:8080 | xargs kill -9
```

**3. Database connection errors:**
- Ensure PostgreSQL is running
- Check database credentials in application.yml
- Verify database `wedding_bazaar` exists

**4. Maven build failures:**
- Clear Maven cache: `mvn clean`
- Update dependencies: `mvn dependency:resolve`
- Check internet connection for dependency downloads

**5. Out of memory errors:**
```bash
# Increase heap size
export MAVEN_OPTS="-Xmx2g -Xms1g"

# Or for specific service
mvn spring-boot:run -Dspring-boot.run.jvmArguments="-Xmx1g -Xms512m"
```

### Service Startup Order:
1. Infrastructure (PostgreSQL, Redis)
2. Service Registry (8761)
3. Config Server (8888)
4. API Gateway (8080)
5. Microservices (8081, 8082, etc.)

### Log Locations:
- Console output shows in terminal
- Application logs in `logs/` directory (if using startup scripts)
- Spring Boot logs in `target/` directory of each service

## 🔄 Development Tips

### Hot Reload:
```bash
mvn spring-boot:run -Dspring-boot.run.jvmArguments="-Dspring.devtools.restart.enabled=true"
```

### Debug Mode:
```bash
mvn spring-boot:run -Dspring-boot.run.jvmArguments="-Xdebug -Xrunjdwp:transport=dt_socket,server=y,suspend=n,address=5005"
```

### Profile-specific Configuration:
```bash
mvn spring-boot:run -Dspring.profiles.active=dev,debug
```

## 📊 Monitoring

### Health Checks:
- Service Registry: http://localhost:8761/actuator/health
- Config Server: http://localhost:8888/actuator/health
- API Gateway: http://localhost:8080/actuator/health
- User Service: http://localhost:8081/actuator/health

### Dashboards:
- Eureka Dashboard: http://localhost:8761
- Swagger UI: http://localhost:8080/swagger-ui.html

## 🛑 Stopping Services

### Manual Stop:
Press `Ctrl+C` in each terminal running a service.

### Stop Docker Infrastructure:
```bash
docker stop postgres-wedding redis-wedding
docker rm postgres-wedding redis-wedding
```

## 📞 Need Help?

1. Check logs for error messages
2. Verify all prerequisites are installed correctly
3. Ensure services start in the correct order
4. Check port availability
5. Verify database connectivity

The application should now be running successfully! 🎉
