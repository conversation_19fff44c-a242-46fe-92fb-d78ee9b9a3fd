# 🚀 How to Run Wedding Bazaar Backend

## 📋 Quick Summary

The Wedding Bazaar Backend is a sophisticated microservices application built with **Java 17**, **Spring Boot 3.2**, and **Spring Cloud**. Here's how to get it running:

## ⚡ Fastest Way to Run

### 1. Test Prerequisites
```bash
# Linux/Mac
./test-setup.sh

# Windows
test-setup.bat
```

### 2. Quick Start
```bash
# Linux/Mac
./quick-start.sh

# Windows
run-application.bat
```

That's it! The application will be running at http://localhost:8080

## 🔧 What You Need Installed

### Required:
- **Java 17+** - Download from [Adoptium](https://adoptium.net/)
- **Maven 3.8+** - Download from [Maven](https://maven.apache.org/)

### Optional (but recommended):
- **Docker** - For easy database setup
- **PostgreSQL** - If not using Docker
- **Redis** - For caching (optional)

## 📊 Service Ports

| Service | Port | URL |
|---------|------|-----|
| Service Registry | 8761 | http://localhost:8761 |
| Config Server | 8888 | http://localhost:8888 |
| API Gateway | 8080 | http://localhost:8080 |
| User Service | 8081 | http://localhost:8081 |
| Vendor Service | 8082 | http://localhost:8082 |
| Search Service | 8083 | http://localhost:8083 |
| Booking Service | 8084 | http://localhost:8084 |
| Payment Service | 8085 | http://localhost:8085 |

## 🧪 Test the Application

### 1. Check Health
```bash
curl http://localhost:8080/actuator/health
```

### 2. View Swagger UI
Open: http://localhost:8080/swagger-ui.html

### 3. Register a User
```bash
curl -X POST http://localhost:8080/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "Test123!@#",
    "firstName": "John",
    "lastName": "Doe"
  }'
```

### 4. Login
```bash
curl -X POST http://localhost:8080/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "Test123!@#"
  }'
```

## 🐛 Common Issues & Solutions

### Issue: "Java not found"
**Solution:** Install Java 17+ and add to PATH
```bash
# Check Java version
java -version
```

### Issue: "Maven not found"
**Solution:** Install Maven and add to PATH
```bash
# Check Maven version
mvn -version
```

### Issue: "Port already in use"
**Solution:** Kill the process using the port
```bash
# Linux/Mac
lsof -ti:8080 | xargs kill -9

# Windows
netstat -ano | findstr :8080
taskkill /PID <PID> /F
```

### Issue: "Database connection failed"
**Solution:** Start PostgreSQL
```bash
# Using Docker (easiest)
docker run -d --name postgres-wedding \
  -e POSTGRES_DB=wedding_bazaar \
  -e POSTGRES_USER=postgres \
  -e POSTGRES_PASSWORD=postgres \
  -p 5432:5432 postgres:15
```

### Issue: "Service won't start"
**Solution:** Check logs and start services in order:
1. Service Registry (8761)
2. Config Server (8888)
3. API Gateway (8080)
4. Other services (8081+)

## 📁 Project Structure

```
WeddingBazaarBackendBest/
├── service-registry/          # Eureka Server
├── config-server/             # Spring Cloud Config
├── api-gateway/               # Spring Cloud Gateway
├── user-service/              # User management
├── vendor-service/            # Vendor management
├── booking-service/           # Booking system
├── search-service/            # Search functionality
├── payment-service/           # Payment processing
├── shared-library/            # Common utilities
├── quick-start.sh            # Linux/Mac startup script
├── run-application.bat       # Windows startup script
├── test-setup.sh            # Prerequisites test (Linux/Mac)
├── test-setup.bat           # Prerequisites test (Windows)
├── INSTALLATION_GUIDE.md    # Detailed setup guide
├── STARTUP_GUIDE.md         # Running instructions
└── README.md                # Main documentation
```

## 🔄 Development Mode

For development with hot reload:
```bash
cd user-service
mvn spring-boot:run -Dspring-boot.run.jvmArguments="-Dspring.devtools.restart.enabled=true"
```

## 🛑 Stopping the Application

### Quick Stop
```bash
./quick-start.sh stop
```

### Manual Stop
Press `Ctrl+C` in each terminal running a service.

### Stop Docker Infrastructure
```bash
docker stop postgres-wedding redis-wedding
docker rm postgres-wedding redis-wedding
```

## 📞 Need Help?

1. **Check Prerequisites:** Run `./test-setup.sh` or `test-setup.bat`
2. **Read Detailed Guides:** See `INSTALLATION_GUIDE.md` and `STARTUP_GUIDE.md`
3. **Check Logs:** Look in `logs/` directory for service logs
4. **Verify Services:** Ensure services start in the correct order
5. **Test Connectivity:** Use health check endpoints

## 🎯 Key Features Demonstrated

- **Microservices Architecture** with Spring Cloud
- **Service Discovery** with Eureka
- **API Gateway** with routing and load balancing
- **Configuration Management** with Spring Cloud Config
- **Advanced Security** with JWT and OAuth2
- **Event-Driven Architecture** with Kafka
- **Caching** with Redis
- **Database per Service** pattern
- **Health Checks** and monitoring
- **Swagger Documentation**

## 🚀 Production Deployment

For production deployment:
- Use `docker-compose.yml` for containerized deployment
- See `k8s/` directory for Kubernetes manifests
- Run `build-images.sh` to build Docker images

---

**🎉 Congratulations!** You now have a fully functional, enterprise-grade microservices backend running locally!
