-- Initialize databases for Wedding Bazaar microservices

-- Create databases for each service
CREATE DATABASE wedding_bazaar_users;
CREATE DATABASE wedding_bazaar_vendors;
CREATE DATABASE wedding_bazaar_bookings;
CREATE DATABASE wedding_bazaar_payments;
CREATE DATABASE wedding_bazaar_analytics;
CREATE DATABASE wedding_bazaar_messaging;
CREATE DATABASE wedding_bazaar_admin;

-- Create users and grant permissions
CREATE USER user_service WITH PASSWORD 'user_service_pass';
CREATE USER vendor_service WITH PASSWORD 'vendor_service_pass';
CREATE USER booking_service WITH PASSWORD 'booking_service_pass';
CREATE USER payment_service WITH PASSWORD 'payment_service_pass';
CREATE USER analytics_service WITH PASSWORD 'analytics_service_pass';
CREATE USER messaging_service WITH PASSWORD 'messaging_service_pass';
CREATE USER admin_service WITH PASSWORD 'admin_service_pass';

-- Grant permissions
GRANT ALL PRIVILEGES ON DATABASE wedding_bazaar_users TO user_service;
GRANT ALL PRIVILEGES ON DATABASE wedding_bazaar_vendors TO vendor_service;
GRANT ALL PRIVILEGES ON DATABASE wedding_bazaar_bookings TO booking_service;
GRANT ALL PRIVILEGES ON DATABASE wedding_bazaar_payments TO payment_service;
GRANT ALL PRIVILEGES ON DATABASE wedding_bazaar_analytics TO analytics_service;
GRANT ALL PRIVILEGES ON DATABASE wedding_bazaar_messaging TO messaging_service;
GRANT ALL PRIVILEGES ON DATABASE wedding_bazaar_admin TO admin_service;
