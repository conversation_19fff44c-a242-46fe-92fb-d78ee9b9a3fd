package com.weddingbazaar.common.security;

import com.auth0.jwt.JWT;
import com.auth0.jwt.JWTVerifier;
import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.exceptions.JWTVerificationException;
import com.auth0.jwt.interfaces.DecodedJWT;
import com.weddingbazaar.common.enums.UserRole;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Date;

/**
 * JWT utility class for token generation and validation
 */
@Component
@Slf4j
public class JwtUtil {
    
    @Value("${jwt.secret:wedding-bazaar-secret-key}")
    private String secretKey;
    
    @Value("${jwt.expiration:86400}") // 24 hours in seconds
    private long jwtExpiration;
    
    @Value("${jwt.refresh-expiration:604800}") // 7 days in seconds
    private long refreshExpiration;
    
    private Algorithm getAlgorithm() {
        return Algorithm.HMAC256(secretKey);
    }
    
    public String generateToken(String userId, String email, UserRole role) {
        return JWT.create()
                .withSubject(userId)
                .withClaim("email", email)
                .withClaim("role", role.name())
                .withIssuedAt(new Date())
                .withExpiresAt(Date.from(Instant.now().plus(jwtExpiration, ChronoUnit.SECONDS)))
                .withIssuer("wedding-bazaar")
                .sign(getAlgorithm());
    }
    
    public String generateRefreshToken(String userId) {
        return JWT.create()
                .withSubject(userId)
                .withIssuedAt(new Date())
                .withExpiresAt(Date.from(Instant.now().plus(refreshExpiration, ChronoUnit.SECONDS)))
                .withIssuer("wedding-bazaar")
                .withClaim("type", "refresh")
                .sign(getAlgorithm());
    }
    
    public boolean validateToken(String token) {
        try {
            JWTVerifier verifier = JWT.require(getAlgorithm())
                    .withIssuer("wedding-bazaar")
                    .build();
            verifier.verify(token);
            return true;
        } catch (JWTVerificationException e) {
            log.error("JWT validation failed: {}", e.getMessage());
            return false;
        }
    }
    
    public String getUserIdFromToken(String token) {
        try {
            DecodedJWT decodedJWT = JWT.decode(token);
            return decodedJWT.getSubject();
        } catch (Exception e) {
            log.error("Error extracting user ID from token: {}", e.getMessage());
            return null;
        }
    }
    
    public String getEmailFromToken(String token) {
        try {
            DecodedJWT decodedJWT = JWT.decode(token);
            return decodedJWT.getClaim("email").asString();
        } catch (Exception e) {
            log.error("Error extracting email from token: {}", e.getMessage());
            return null;
        }
    }
    
    public UserRole getRoleFromToken(String token) {
        try {
            DecodedJWT decodedJWT = JWT.decode(token);
            String role = decodedJWT.getClaim("role").asString();
            return UserRole.valueOf(role);
        } catch (Exception e) {
            log.error("Error extracting role from token: {}", e.getMessage());
            return null;
        }
    }
    
    public boolean isTokenExpired(String token) {
        try {
            DecodedJWT decodedJWT = JWT.decode(token);
            return decodedJWT.getExpiresAt().before(new Date());
        } catch (Exception e) {
            log.error("Error checking token expiration: {}", e.getMessage());
            return true;
        }
    }
    
    public boolean isRefreshToken(String token) {
        try {
            DecodedJWT decodedJWT = JWT.decode(token);
            String type = decodedJWT.getClaim("type").asString();
            return "refresh".equals(type);
        } catch (Exception e) {
            return false;
        }
    }
}
