package com.wedding.user.event;

import com.wedding.user.audit.UserSnapshot;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Base interface for all user domain events
 */
public interface UserDomainEvent {
    String getEventId();
    String getUserId();
    LocalDateTime getTimestamp();
}

/**
 * Event published when a new user registers
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserRegisteredEvent implements UserDomainEvent {
    private String eventId;
    private String userId;
    private String email;
    private String firstName;
    private String lastName;
    private String phoneNumber;
    private String registrationSource;
    private String ipAddress;
    private String userAgent;
    private LocalDateTime timestamp;
}

/**
 * Event published when user profile is updated
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserUpdatedEvent implements UserDomainEvent {
    private String eventId;
    private String userId;
    private UserSnapshot beforeSnapshot;
    private UserSnapshot afterSnapshot;
    private List<String> changedFields;
    private String updatedBy;
    private String updateReason;
    private LocalDateTime timestamp;
}

/**
 * Event published when user email is verified
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EmailVerifiedEvent implements UserDomainEvent {
    private String eventId;
    private String userId;
    private String email;
    private String verificationToken;
    private LocalDateTime verifiedAt;
    private String verificationMethod;
    private LocalDateTime timestamp;
}

/**
 * Event published when user password is changed
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PasswordChangedEvent implements UserDomainEvent {
    private String eventId;
    private String userId;
    private String email;
    private LocalDateTime changedAt;
    private String changeReason;
    private String ipAddress;
    private boolean forcedChange;
    private LocalDateTime timestamp;
}

/**
 * Event published when user account is locked
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserLockedEvent implements UserDomainEvent {
    private String eventId;
    private String userId;
    private String email;
    private String reason;
    private String lockedBy;
    private LocalDateTime lockedAt;
    private LocalDateTime unlockAt;
    private boolean permanent;
    private LocalDateTime timestamp;
}

/**
 * Event published when user account is unlocked
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserUnlockedEvent implements UserDomainEvent {
    private String eventId;
    private String userId;
    private String email;
    private String reason;
    private String unlockedBy;
    private LocalDateTime unlockedAt;
    private LocalDateTime timestamp;
}

/**
 * Event published when user logs in
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserLoginEvent implements UserDomainEvent {
    private String eventId;
    private String userId;
    private String email;
    private String sessionId;
    private String ipAddress;
    private String userAgent;
    private String deviceInfo;
    private String location;
    private boolean successful;
    private String failureReason;
    private LocalDateTime loginAt;
    private LocalDateTime timestamp;
}

/**
 * Event published when user logs out
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserLogoutEvent implements UserDomainEvent {
    private String eventId;
    private String userId;
    private String sessionId;
    private LocalDateTime logoutAt;
    private String logoutReason;
    private boolean forced;
    private LocalDateTime timestamp;
}

/**
 * Event published when user role is changed
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserRoleChangedEvent implements UserDomainEvent {
    private String eventId;
    private String userId;
    private String email;
    private List<String> previousRoles;
    private List<String> newRoles;
    private String changedBy;
    private String changeReason;
    private LocalDateTime changedAt;
    private LocalDateTime timestamp;
}

/**
 * Event published when user attempts failed login
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserLoginFailedEvent implements UserDomainEvent {
    private String eventId;
    private String userId;
    private String email;
    private String ipAddress;
    private String userAgent;
    private String failureReason;
    private int attemptCount;
    private boolean accountLocked;
    private LocalDateTime attemptAt;
    private LocalDateTime timestamp;
}

/**
 * Event published when user requests password reset
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PasswordResetRequestedEvent implements UserDomainEvent {
    private String eventId;
    private String userId;
    private String email;
    private String resetToken;
    private String ipAddress;
    private String userAgent;
    private LocalDateTime requestedAt;
    private LocalDateTime expiresAt;
    private LocalDateTime timestamp;
}

/**
 * Event published when password reset is completed
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PasswordResetCompletedEvent implements UserDomainEvent {
    private String eventId;
    private String userId;
    private String email;
    private String resetToken;
    private String ipAddress;
    private LocalDateTime completedAt;
    private LocalDateTime timestamp;
}

/**
 * Event published when user profile is deleted
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserDeletedEvent implements UserDomainEvent {
    private String eventId;
    private String userId;
    private String email;
    private String deletionReason;
    private String deletedBy;
    private boolean softDelete;
    private UserSnapshot finalSnapshot;
    private LocalDateTime deletedAt;
    private LocalDateTime timestamp;
}

/**
 * Event published when user consents to terms
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserConsentEvent implements UserDomainEvent {
    private String eventId;
    private String userId;
    private String email;
    private String consentType;
    private String consentVersion;
    private boolean granted;
    private String ipAddress;
    private String userAgent;
    private LocalDateTime consentAt;
    private LocalDateTime timestamp;
}

/**
 * Event published when user data is exported (GDPR)
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserDataExportedEvent implements UserDomainEvent {
    private String eventId;
    private String userId;
    private String email;
    private String exportFormat;
    private String exportLocation;
    private List<String> exportedDataTypes;
    private String requestedBy;
    private LocalDateTime exportedAt;
    private LocalDateTime timestamp;
}

/**
 * Event published when user session expires
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserSessionExpiredEvent implements UserDomainEvent {
    private String eventId;
    private String userId;
    private String sessionId;
    private String expireReason;
    private LocalDateTime sessionStartAt;
    private LocalDateTime expiredAt;
    private long sessionDurationMinutes;
    private LocalDateTime timestamp;
}

/**
 * Event published when user preferences are updated
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserPreferencesUpdatedEvent implements UserDomainEvent {
    private String eventId;
    private String userId;
    private String email;
    private java.util.Map<String, Object> previousPreferences;
    private java.util.Map<String, Object> newPreferences;
    private List<String> changedPreferences;
    private LocalDateTime updatedAt;
    private LocalDateTime timestamp;
}

/**
 * Event published when user avatar/profile image is updated
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserAvatarUpdatedEvent implements UserDomainEvent {
    private String eventId;
    private String userId;
    private String email;
    private String previousAvatarUrl;
    private String newAvatarUrl;
    private String uploadSource;
    private long fileSize;
    private String fileType;
    private LocalDateTime updatedAt;
    private LocalDateTime timestamp;
}
