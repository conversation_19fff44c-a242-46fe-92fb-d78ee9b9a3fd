package com.wedding.user.cache;

import com.wedding.user.dto.UserResponse;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

/**
 * Advanced Redis-based cache manager for user data with sophisticated caching strategies
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class UserCacheManager {
    
    private final RedisTemplate<String, Object> redisTemplate;
    private final RedisTemplate<String, String> stringRedisTemplate;
    private final ObjectMapper objectMapper;
    
    // Cache key prefixes
    private static final String USER_PREFIX = "user:";
    private static final String USER_SESSION_PREFIX = "user:session:";
    private static final String USER_LOGIN_ATTEMPTS_PREFIX = "user:login_attempts:";
    private static final String USER_RATE_LIMIT_PREFIX = "user:rate_limit:";
    private static final String USER_PREFERENCES_PREFIX = "user:preferences:";
    private static final String USER_PERMISSIONS_PREFIX = "user:permissions:";
    private static final String USER_ACTIVITY_PREFIX = "user:activity:";
    
    // Cache TTL configurations
    private static final Duration USER_CACHE_TTL = Duration.ofHours(2);
    private static final Duration SESSION_CACHE_TTL = Duration.ofHours(24);
    private static final Duration LOGIN_ATTEMPTS_TTL = Duration.ofMinutes(30);
    private static final Duration RATE_LIMIT_TTL = Duration.ofMinutes(60);
    private static final Duration PREFERENCES_TTL = Duration.ofHours(6);
    private static final Duration PERMISSIONS_TTL = Duration.ofHours(1);
    private static final Duration ACTIVITY_TTL = Duration.ofDays(7);
    
    /**
     * Caches user data with automatic expiration
     */
    public void putUser(String userId, UserResponse user) {
        try {
            String key = USER_PREFIX + userId;
            String userJson = objectMapper.writeValueAsString(user);
            
            redisTemplate.opsForValue().set(key, userJson, USER_CACHE_TTL);
            
            // Also cache user by email for quick lookup
            String emailKey = "user:email:" + user.getEmail();
            redisTemplate.opsForValue().set(emailKey, userId, USER_CACHE_TTL);
            
            log.debug("Cached user: {}", userId);
            
        } catch (JsonProcessingException e) {
            log.error("Failed to cache user: {}", userId, e);
        }
    }
    
    /**
     * Retrieves user from cache
     */
    public Optional<UserResponse> getUser(String userId) {
        try {
            String key = USER_PREFIX + userId;
            String userJson = (String) redisTemplate.opsForValue().get(key);
            
            if (userJson != null) {
                UserResponse user = objectMapper.readValue(userJson, UserResponse.class);
                log.debug("Retrieved user from cache: {}", userId);
                return Optional.of(user);
            }
            
        } catch (JsonProcessingException e) {
            log.error("Failed to deserialize cached user: {}", userId, e);
        }
        
        return Optional.empty();
    }
    
    /**
     * Retrieves user ID by email
     */
    public Optional<String> getUserIdByEmail(String email) {
        String emailKey = "user:email:" + email;
        String userId = (String) redisTemplate.opsForValue().get(emailKey);
        return Optional.ofNullable(userId);
    }
    
    /**
     * Asynchronously caches user data
     */
    public CompletableFuture<Void> putUserAsync(String userId, UserResponse user) {
        return CompletableFuture.runAsync(() -> putUser(userId, user));
    }
    
    /**
     * Invalidates user cache
     */
    public void invalidateUser(String userId) {
        String key = USER_PREFIX + userId;
        redisTemplate.delete(key);
        
        // Also invalidate related caches
        invalidateUserPermissions(userId);
        invalidateUserPreferences(userId);
        
        log.debug("Invalidated user cache: {}", userId);
    }
    
    /**
     * Manages user session cache
     */
    public void putSession(String sessionId, String userId, Map<String, Object> sessionData) {
        try {
            String key = USER_SESSION_PREFIX + sessionId;
            String sessionJson = objectMapper.writeValueAsString(sessionData);
            
            redisTemplate.opsForValue().set(key, sessionJson, SESSION_CACHE_TTL);
            
            // Maintain user -> sessions mapping
            String userSessionsKey = "user:sessions:" + userId;
            redisTemplate.opsForSet().add(userSessionsKey, sessionId);
            redisTemplate.expire(userSessionsKey, SESSION_CACHE_TTL);
            
            log.debug("Cached session: {} for user: {}", sessionId, userId);
            
        } catch (JsonProcessingException e) {
            log.error("Failed to cache session: {}", sessionId, e);
        }
    }
    
    /**
     * Retrieves session data
     */
    public Optional<Map<String, Object>> getSession(String sessionId) {
        try {
            String key = USER_SESSION_PREFIX + sessionId;
            String sessionJson = (String) redisTemplate.opsForValue().get(key);
            
            if (sessionJson != null) {
                @SuppressWarnings("unchecked")
                Map<String, Object> sessionData = objectMapper.readValue(sessionJson, Map.class);
                return Optional.of(sessionData);
            }
            
        } catch (JsonProcessingException e) {
            log.error("Failed to deserialize session: {}", sessionId, e);
        }
        
        return Optional.empty();
    }
    
    /**
     * Removes session from cache
     */
    public void removeSession(String sessionId) {
        String key = USER_SESSION_PREFIX + sessionId;
        redisTemplate.delete(key);
        log.debug("Removed session: {}", sessionId);
    }
    
    /**
     * Invalidates all sessions for a user
     */
    public void invalidateUserSessions(String userId) {
        String userSessionsKey = "user:sessions:" + userId;
        Set<Object> sessionIds = redisTemplate.opsForSet().members(userSessionsKey);
        
        if (sessionIds != null && !sessionIds.isEmpty()) {
            List<String> sessionKeys = sessionIds.stream()
                    .map(sessionId -> USER_SESSION_PREFIX + sessionId)
                    .toList();
            
            redisTemplate.delete(sessionKeys);
            redisTemplate.delete(userSessionsKey);
            
            log.debug("Invalidated {} sessions for user: {}", sessionIds.size(), userId);
        }
    }
    
    /**
     * Manages login attempt tracking with sliding window
     */
    public void recordLoginAttempt(String userId, boolean successful) {
        String key = USER_LOGIN_ATTEMPTS_PREFIX + userId;
        LocalDateTime now = LocalDateTime.now();
        
        if (successful) {
            // Clear failed attempts on successful login
            redisTemplate.delete(key);
        } else {
            // Add failed attempt with timestamp
            redisTemplate.opsForZSet().add(key, now.toString(), now.toEpochSecond(java.time.ZoneOffset.UTC));
            redisTemplate.expire(key, LOGIN_ATTEMPTS_TTL);
            
            // Remove old attempts (older than 30 minutes)
            long cutoffTime = now.minusMinutes(30).toEpochSecond(java.time.ZoneOffset.UTC);
            redisTemplate.opsForZSet().removeRangeByScore(key, 0, cutoffTime);
        }
    }
    
    /**
     * Gets failed login attempt count in the last 30 minutes
     */
    public long getFailedLoginAttempts(String userId) {
        String key = USER_LOGIN_ATTEMPTS_PREFIX + userId;
        Long count = redisTemplate.opsForZSet().count(key, 0, Double.MAX_VALUE);
        return count != null ? count : 0;
    }
    
    /**
     * Implements rate limiting using sliding window
     */
    public boolean isRateLimited(String userId, String action, int maxAttempts, Duration window) {
        String key = USER_RATE_LIMIT_PREFIX + userId + ":" + action;
        LocalDateTime now = LocalDateTime.now();
        long currentTime = now.toEpochSecond(java.time.ZoneOffset.UTC);
        long windowStart = now.minus(window).toEpochSecond(java.time.ZoneOffset.UTC);
        
        // Remove old entries
        redisTemplate.opsForZSet().removeRangeByScore(key, 0, windowStart);
        
        // Count current attempts
        Long currentAttempts = redisTemplate.opsForZSet().count(key, windowStart, currentTime);
        
        if (currentAttempts != null && currentAttempts >= maxAttempts) {
            return true; // Rate limited
        }
        
        // Add current attempt
        redisTemplate.opsForZSet().add(key, UUID.randomUUID().toString(), currentTime);
        redisTemplate.expire(key, window);
        
        return false; // Not rate limited
    }
    
    /**
     * Caches user preferences
     */
    public void putUserPreferences(String userId, Map<String, Object> preferences) {
        try {
            String key = USER_PREFERENCES_PREFIX + userId;
            String preferencesJson = objectMapper.writeValueAsString(preferences);
            
            redisTemplate.opsForValue().set(key, preferencesJson, PREFERENCES_TTL);
            log.debug("Cached preferences for user: {}", userId);
            
        } catch (JsonProcessingException e) {
            log.error("Failed to cache preferences for user: {}", userId, e);
        }
    }
    
    /**
     * Retrieves user preferences
     */
    public Optional<Map<String, Object>> getUserPreferences(String userId) {
        try {
            String key = USER_PREFERENCES_PREFIX + userId;
            String preferencesJson = (String) redisTemplate.opsForValue().get(key);
            
            if (preferencesJson != null) {
                @SuppressWarnings("unchecked")
                Map<String, Object> preferences = objectMapper.readValue(preferencesJson, Map.class);
                return Optional.of(preferences);
            }
            
        } catch (JsonProcessingException e) {
            log.error("Failed to deserialize preferences for user: {}", userId, e);
        }
        
        return Optional.empty();
    }
    
    /**
     * Invalidates user preferences cache
     */
    public void invalidateUserPreferences(String userId) {
        String key = USER_PREFERENCES_PREFIX + userId;
        redisTemplate.delete(key);
        log.debug("Invalidated preferences cache for user: {}", userId);
    }
    
    /**
     * Caches user permissions
     */
    public void putUserPermissions(String userId, Set<String> permissions) {
        String key = USER_PERMISSIONS_PREFIX + userId;
        redisTemplate.opsForSet().add(key, permissions.toArray());
        redisTemplate.expire(key, PERMISSIONS_TTL);
        log.debug("Cached permissions for user: {}", userId);
    }
    
    /**
     * Retrieves user permissions
     */
    public Set<String> getUserPermissions(String userId) {
        String key = USER_PERMISSIONS_PREFIX + userId;
        Set<Object> permissions = redisTemplate.opsForSet().members(key);
        
        if (permissions != null) {
            return permissions.stream()
                    .map(Object::toString)
                    .collect(java.util.stream.Collectors.toSet());
        }
        
        return Collections.emptySet();
    }
    
    /**
     * Invalidates user permissions cache
     */
    public void invalidateUserPermissions(String userId) {
        String key = USER_PERMISSIONS_PREFIX + userId;
        redisTemplate.delete(key);
        log.debug("Invalidated permissions cache for user: {}", userId);
    }
    
    /**
     * Records user activity for analytics
     */
    public void recordUserActivity(String userId, String activity, Map<String, Object> metadata) {
        try {
            String key = USER_ACTIVITY_PREFIX + userId;
            LocalDateTime now = LocalDateTime.now();
            
            Map<String, Object> activityData = new HashMap<>();
            activityData.put("activity", activity);
            activityData.put("timestamp", now);
            activityData.put("metadata", metadata);
            
            String activityJson = objectMapper.writeValueAsString(activityData);
            
            // Use sorted set with timestamp as score for chronological ordering
            redisTemplate.opsForZSet().add(key, activityJson, now.toEpochSecond(java.time.ZoneOffset.UTC));
            redisTemplate.expire(key, ACTIVITY_TTL);
            
            // Keep only last 100 activities
            redisTemplate.opsForZSet().removeRange(key, 0, -101);
            
        } catch (JsonProcessingException e) {
            log.error("Failed to record activity for user: {}", userId, e);
        }
    }
    
    /**
     * Updates last login timestamp
     */
    public void updateLastLogin(String userId, LocalDateTime loginTime) {
        String key = "user:last_login:" + userId;
        redisTemplate.opsForValue().set(key, loginTime.toString(), Duration.ofDays(30));
    }
    
    /**
     * Initializes cache for a new user
     */
    public void initializeUserCache(String userId) {
        // Pre-warm cache with empty structures
        putUserPermissions(userId, Collections.emptySet());
        putUserPreferences(userId, Collections.emptyMap());
        
        log.debug("Initialized cache for user: {}", userId);
    }
    
    /**
     * Performs cache warming for frequently accessed users
     */
    public void warmCache(List<String> userIds) {
        CompletableFuture.runAsync(() -> {
            log.info("Starting cache warming for {} users", userIds.size());
            
            for (String userId : userIds) {
                try {
                    // This would typically fetch from database and cache
                    initializeUserCache(userId);
                    Thread.sleep(10); // Small delay to avoid overwhelming the system
                } catch (Exception e) {
                    log.warn("Failed to warm cache for user: {}", userId, e);
                }
            }
            
            log.info("Cache warming completed");
        });
    }
    
    /**
     * Gets cache statistics
     */
    public Map<String, Object> getCacheStatistics() {
        Map<String, Object> stats = new HashMap<>();
        
        // Count different types of cached data
        Set<String> userKeys = stringRedisTemplate.keys(USER_PREFIX + "*");
        Set<String> sessionKeys = stringRedisTemplate.keys(USER_SESSION_PREFIX + "*");
        Set<String> preferencesKeys = stringRedisTemplate.keys(USER_PREFERENCES_PREFIX + "*");
        Set<String> permissionsKeys = stringRedisTemplate.keys(USER_PERMISSIONS_PREFIX + "*");
        
        stats.put("cachedUsers", userKeys != null ? userKeys.size() : 0);
        stats.put("activeSessions", sessionKeys != null ? sessionKeys.size() : 0);
        stats.put("cachedPreferences", preferencesKeys != null ? preferencesKeys.size() : 0);
        stats.put("cachedPermissions", permissionsKeys != null ? permissionsKeys.size() : 0);
        
        return stats;
    }
}
