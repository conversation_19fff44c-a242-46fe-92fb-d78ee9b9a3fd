server:
  port: 8761

spring:
  application:
    name: service-registry
  security:
    user:
      name: admin
      password: admin123
      roles: ADMIN

eureka:
  instance:
    hostname: localhost
    prefer-ip-address: false
  client:
    register-with-eureka: false
    fetch-registry: false
    service-url:
      defaultZone: ************************************/eureka/
  server:
    enable-self-preservation: false
    eviction-interval-timer-in-ms: 10000
    renewal-percent-threshold: 0.85
    response-cache-update-interval-ms: 5000
    response-cache-auto-expiration-in-seconds: 180

management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus,eureka
  endpoint:
    health:
      show-details: always
    eureka:
      enabled: true

logging:
  level:
    com.netflix.eureka: INFO
    com.netflix.discovery: INFO
    org.springframework.cloud.netflix.eureka: INFO
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
