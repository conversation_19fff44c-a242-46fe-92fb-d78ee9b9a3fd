#!/bin/bash

# Wedding Bazaar Backend Services Startup Script
# This script starts all microservices in the correct order

set -e

echo "🎉 Starting Wedding Bazaar Backend Services..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}$1${NC}"
}

# Function to check if a service is running
check_service() {
    local service_name=$1
    local port=$2
    local max_attempts=30
    local attempt=1

    print_status "Waiting for $service_name to start on port $port..."
    
    while [ $attempt -le $max_attempts ]; do
        if curl -s http://localhost:$port/actuator/health > /dev/null 2>&1; then
            print_status "$service_name is running!"
            return 0
        fi
        
        echo -n "."
        sleep 2
        attempt=$((attempt + 1))
    done
    
    print_error "$service_name failed to start within expected time"
    return 1
}

# Function to build a service
build_service() {
    local service_name=$1
    print_status "Building $service_name..."
    
    cd $service_name
    if mvn clean package -DskipTests > /dev/null 2>&1; then
        print_status "$service_name built successfully"
    else
        print_error "Failed to build $service_name"
        exit 1
    fi
    cd ..
}

# Function to start a service
start_service() {
    local service_name=$1
    local port=$2
    
    print_status "Starting $service_name..."
    
    cd $service_name
    nohup java -jar target/*.jar > logs/$service_name.log 2>&1 &
    echo $! > $service_name.pid
    cd ..
    
    check_service $service_name $port
}

# Create logs directory
mkdir -p logs

print_header "=== Wedding Bazaar Backend Startup ==="

# Step 1: Start Infrastructure Services
print_header "\n🔧 Step 1: Starting Infrastructure Services"
print_status "Starting Docker infrastructure..."

if ! docker-compose up -d; then
    print_error "Failed to start infrastructure services"
    exit 1
fi

print_status "Waiting for infrastructure services to be ready..."
sleep 30

# Check infrastructure services
print_status "Checking PostgreSQL..."
until docker exec wedding-bazaar-postgres pg_isready -U postgres > /dev/null 2>&1; do
    echo -n "."
    sleep 2
done
print_status "PostgreSQL is ready!"

print_status "Checking Redis..."
until docker exec wedding-bazaar-redis redis-cli ping > /dev/null 2>&1; do
    echo -n "."
    sleep 2
done
print_status "Redis is ready!"

print_status "Checking Elasticsearch..."
until curl -s http://localhost:9200/_cluster/health > /dev/null 2>&1; do
    echo -n "."
    sleep 2
done
print_status "Elasticsearch is ready!"

print_status "Checking Kafka..."
sleep 10  # Kafka takes a bit longer to start
print_status "Kafka is ready!"

# Step 2: Build All Services
print_header "\n🔨 Step 2: Building All Services"

# Build common module first
print_status "Building common module..."
cd common
mvn clean install -DskipTests > /dev/null 2>&1
cd ..

# Build all services
services=("eureka-server" "config-server" "api-gateway" "user-service" "vendor-service" "booking-service" "search-service" "payment-service" "messaging-service" "analytics-service" "admin-service" "notification-service")

for service in "${services[@]}"; do
    build_service $service
done

# Step 3: Start Core Services
print_header "\n🚀 Step 3: Starting Core Services"

# Start Eureka Server first
start_service "eureka-server" 8761

# Start Config Server
start_service "config-server" 8888

# Wait a bit for service discovery to stabilize
sleep 10

# Step 4: Start Business Services
print_header "\n💼 Step 4: Starting Business Services"

# Start services in dependency order
start_service "user-service" 8081
start_service "vendor-service" 8082
start_service "search-service" 8084
start_service "payment-service" 8085
start_service "booking-service" 8083
start_service "messaging-service" 8086
start_service "analytics-service" 8087
start_service "admin-service" 8088
start_service "notification-service" 8089

# Step 5: Start API Gateway
print_header "\n🌐 Step 5: Starting API Gateway"
start_service "api-gateway" 8080

# Step 6: Final Health Check
print_header "\n✅ Step 6: Final Health Check"

print_status "Performing final health check..."

all_healthy=true

for service in "${services[@]}"; do
    case $service in
        "eureka-server") port=8761 ;;
        "config-server") port=8888 ;;
        "api-gateway") port=8080 ;;
        "user-service") port=8081 ;;
        "vendor-service") port=8082 ;;
        "booking-service") port=8083 ;;
        "search-service") port=8084 ;;
        "payment-service") port=8085 ;;
        "messaging-service") port=8086 ;;
        "analytics-service") port=8087 ;;
        "admin-service") port=8088 ;;
        "notification-service") port=8089 ;;
    esac
    
    if curl -s http://localhost:$port/actuator/health | grep -q "UP"; then
        print_status "$service is healthy ✓"
    else
        print_error "$service is not healthy ✗"
        all_healthy=false
    fi
done

# Summary
print_header "\n📊 Startup Summary"

if $all_healthy; then
    print_status "🎉 All services started successfully!"
    echo ""
    echo "Service URLs:"
    echo "  • Eureka Dashboard: http://localhost:8761"
    echo "  • API Gateway: http://localhost:8080"
    echo "  • Grafana: http://localhost:3000 (admin/admin123)"
    echo "  • Kibana: http://localhost:5601"
    echo "  • Kafka UI: http://localhost:8090"
    echo "  • Prometheus: http://localhost:9090"
    echo ""
    echo "API Endpoints:"
    echo "  • User API: http://localhost:8080/api/users"
    echo "  • Vendor API: http://localhost:8080/api/vendors"
    echo "  • Booking API: http://localhost:8080/api/bookings"
    echo "  • Search API: http://localhost:8080/api/search"
    echo "  • Payment API: http://localhost:8080/api/payments"
    echo ""
    print_status "Wedding Bazaar Backend is ready for use! 🚀"
else
    print_error "Some services failed to start properly. Check the logs for details."
    echo ""
    echo "Log files are available in the logs/ directory:"
    ls -la logs/
    exit 1
fi
