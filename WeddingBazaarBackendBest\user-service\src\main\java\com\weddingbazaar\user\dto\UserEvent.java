package com.weddingbazaar.user.dto;

import com.weddingbazaar.common.enums.UserRole;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * User event DTO for Kafka messaging
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserEvent {
    
    private String eventType;
    private String userId;
    private String email;
    private UserRole role;
    private LocalDateTime timestamp;
}
