package com.weddingbazaar.payment.service;

import com.stripe.Stripe;
import com.stripe.exception.StripeException;
import com.stripe.model.PaymentIntent;
import com.stripe.model.Refund;
import com.stripe.model.checkout.Session;
import com.stripe.param.PaymentIntentCreateParams;
import com.stripe.param.RefundCreateParams;
import com.stripe.param.checkout.SessionCreateParams;
import com.weddingbazaar.common.exception.BusinessException;
import com.weddingbazaar.payment.dto.PaymentRequest;
import com.weddingbazaar.payment.dto.PaymentResponse;
import com.weddingbazaar.payment.dto.RefundRequest;
import com.weddingbazaar.payment.entity.Payment;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

/**
 * Stripe payment service implementation
 */
@Service
@Slf4j
public class StripePaymentService {
    
    @Value("${stripe.secret-key}")
    private String stripeSecretKey;
    
    @Value("${stripe.webhook-secret}")
    private String webhookSecret;
    
    @Value("${app.frontend.success-url}")
    private String successUrl;
    
    @Value("${app.frontend.cancel-url}")
    private String cancelUrl;
    
    @PostConstruct
    public void init() {
        Stripe.apiKey = stripeSecretKey;
    }
    
    public PaymentResponse createCheckoutSession(PaymentRequest request) {
        try {
            log.info("Creating Stripe checkout session for booking: {}", request.getBookingId());
            
            // Calculate amounts
            BigDecimal amount = request.getAmount();
            BigDecimal platformFee = calculatePlatformFee(amount);
            BigDecimal vendorAmount = amount.subtract(platformFee);
            
            SessionCreateParams.Builder paramsBuilder = SessionCreateParams.builder()
                    .setMode(SessionCreateParams.Mode.PAYMENT)
                    .setSuccessUrl(successUrl + "?session_id={CHECKOUT_SESSION_ID}")
                    .setCancelUrl(cancelUrl)
                    .addPaymentMethodType(SessionCreateParams.PaymentMethodType.CARD);
            
            // Add line item
            SessionCreateParams.LineItem lineItem = SessionCreateParams.LineItem.builder()
                    .setPriceData(SessionCreateParams.LineItem.PriceData.builder()
                            .setCurrency(request.getCurrency().toLowerCase())
                            .setProductData(SessionCreateParams.LineItem.PriceData.ProductData.builder()
                                    .setName("Wedding Service Booking")
                                    .setDescription(request.getDescription())
                                    .build())
                            .setUnitAmount(amount.multiply(new BigDecimal("100")).longValue()) // Convert to cents
                            .build())
                    .setQuantity(1L)
                    .build();
            
            paramsBuilder.addLineItem(lineItem);
            
            // Add metadata
            Map<String, String> metadata = new HashMap<>();
            metadata.put("booking_id", request.getBookingId());
            metadata.put("user_id", request.getUserId());
            metadata.put("vendor_id", request.getVendorId());
            metadata.put("platform_fee", platformFee.toString());
            metadata.put("vendor_amount", vendorAmount.toString());
            
            paramsBuilder.putAllMetadata(metadata);
            
            // Create session
            Session session = Session.create(paramsBuilder.build());
            
            return PaymentResponse.builder()
                    .sessionId(session.getId())
                    .sessionUrl(session.getUrl())
                    .paymentIntentId(session.getPaymentIntent())
                    .amount(amount)
                    .currency(request.getCurrency())
                    .status("pending")
                    .build();
            
        } catch (StripeException e) {
            log.error("Error creating Stripe checkout session: {}", e.getMessage(), e);
            throw new BusinessException("Failed to create payment session", "PAYMENT_SESSION_ERROR");
        }
    }
    
    public PaymentResponse createPaymentIntent(PaymentRequest request) {
        try {
            log.info("Creating Stripe payment intent for booking: {}", request.getBookingId());
            
            BigDecimal amount = request.getAmount();
            BigDecimal platformFee = calculatePlatformFee(amount);
            BigDecimal vendorAmount = amount.subtract(platformFee);
            
            PaymentIntentCreateParams.Builder paramsBuilder = PaymentIntentCreateParams.builder()
                    .setAmount(amount.multiply(new BigDecimal("100")).longValue()) // Convert to cents
                    .setCurrency(request.getCurrency().toLowerCase())
                    .setDescription(request.getDescription())
                    .setConfirmationMethod(PaymentIntentCreateParams.ConfirmationMethod.MANUAL)
                    .setConfirm(false);
            
            // Add metadata
            Map<String, String> metadata = new HashMap<>();
            metadata.put("booking_id", request.getBookingId());
            metadata.put("user_id", request.getUserId());
            metadata.put("vendor_id", request.getVendorId());
            metadata.put("platform_fee", platformFee.toString());
            metadata.put("vendor_amount", vendorAmount.toString());
            
            paramsBuilder.putAllMetadata(metadata);
            
            PaymentIntent paymentIntent = PaymentIntent.create(paramsBuilder.build());
            
            return PaymentResponse.builder()
                    .paymentIntentId(paymentIntent.getId())
                    .clientSecret(paymentIntent.getClientSecret())
                    .amount(amount)
                    .currency(request.getCurrency())
                    .status(paymentIntent.getStatus())
                    .build();
            
        } catch (StripeException e) {
            log.error("Error creating Stripe payment intent: {}", e.getMessage(), e);
            throw new BusinessException("Failed to create payment intent", "PAYMENT_INTENT_ERROR");
        }
    }
    
    public PaymentResponse confirmPaymentIntent(String paymentIntentId) {
        try {
            log.info("Confirming Stripe payment intent: {}", paymentIntentId);
            
            PaymentIntent paymentIntent = PaymentIntent.retrieve(paymentIntentId);
            paymentIntent = paymentIntent.confirm();
            
            return PaymentResponse.builder()
                    .paymentIntentId(paymentIntent.getId())
                    .amount(new BigDecimal(paymentIntent.getAmount()).divide(new BigDecimal("100")))
                    .currency(paymentIntent.getCurrency().toUpperCase())
                    .status(paymentIntent.getStatus())
                    .build();
            
        } catch (StripeException e) {
            log.error("Error confirming Stripe payment intent: {}", e.getMessage(), e);
            throw new BusinessException("Failed to confirm payment", "PAYMENT_CONFIRMATION_ERROR");
        }
    }
    
    public PaymentResponse processRefund(RefundRequest request) {
        try {
            log.info("Processing Stripe refund for payment: {}", request.getPaymentId());
            
            RefundCreateParams.Builder paramsBuilder = RefundCreateParams.builder()
                    .setPaymentIntent(request.getProviderPaymentId())
                    .setReason(RefundCreateParams.Reason.REQUESTED_BY_CUSTOMER);
            
            if (request.getAmount() != null) {
                paramsBuilder.setAmount(request.getAmount().multiply(new BigDecimal("100")).longValue());
            }
            
            if (request.getReason() != null) {
                Map<String, String> metadata = new HashMap<>();
                metadata.put("refund_reason", request.getReason());
                paramsBuilder.putAllMetadata(metadata);
            }
            
            Refund refund = Refund.create(paramsBuilder.build());
            
            return PaymentResponse.builder()
                    .refundId(refund.getId())
                    .amount(new BigDecimal(refund.getAmount()).divide(new BigDecimal("100")))
                    .currency(refund.getCurrency().toUpperCase())
                    .status(refund.getStatus())
                    .build();
            
        } catch (StripeException e) {
            log.error("Error processing Stripe refund: {}", e.getMessage(), e);
            throw new BusinessException("Failed to process refund", "REFUND_ERROR");
        }
    }
    
    public PaymentIntent retrievePaymentIntent(String paymentIntentId) {
        try {
            return PaymentIntent.retrieve(paymentIntentId);
        } catch (StripeException e) {
            log.error("Error retrieving Stripe payment intent: {}", e.getMessage(), e);
            throw new BusinessException("Failed to retrieve payment intent", "PAYMENT_RETRIEVAL_ERROR");
        }
    }
    
    public Session retrieveSession(String sessionId) {
        try {
            return Session.retrieve(sessionId);
        } catch (StripeException e) {
            log.error("Error retrieving Stripe session: {}", e.getMessage(), e);
            throw new BusinessException("Failed to retrieve session", "SESSION_RETRIEVAL_ERROR");
        }
    }
    
    private BigDecimal calculatePlatformFee(BigDecimal amount) {
        // Calculate 10% platform fee
        return amount.multiply(new BigDecimal("0.10"));
    }
}
