#!/bin/bash

# Wedding Bazaar Backend - Docker Image Build Script
# This script builds all Docker images for the microservices

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
REGISTRY="wedding-bazaar"
VERSION=${1:-latest}
BUILD_ARGS=""

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to build Docker image
build_image() {
    local service_name=$1
    local dockerfile_path=$2
    local image_name="${REGISTRY}/${service_name}:${VERSION}"
    
    print_status "Building ${service_name} image..."
    
    if docker build -t "${image_name}" -f "${dockerfile_path}" ${BUILD_ARGS} .; then
        print_success "Successfully built ${image_name}"
        
        # Tag as latest if version is not latest
        if [ "${VERSION}" != "latest" ]; then
            docker tag "${image_name}" "${REGISTRY}/${service_name}:latest"
            print_status "Tagged ${image_name} as latest"
        fi
    else
        print_error "Failed to build ${service_name}"
        exit 1
    fi
}

# Function to check if Docker is running
check_docker() {
    if ! docker info > /dev/null 2>&1; then
        print_error "Docker is not running. Please start Docker and try again."
        exit 1
    fi
}

# Function to clean up old images
cleanup_images() {
    print_status "Cleaning up old images..."
    
    # Remove dangling images
    if docker images -f "dangling=true" -q | grep -q .; then
        docker rmi $(docker images -f "dangling=true" -q) 2>/dev/null || true
        print_success "Removed dangling images"
    fi
    
    # Remove old versions (keep latest 3)
    for service in service-registry config-server api-gateway user-service vendor-service search-service booking-service payment-service; do
        old_images=$(docker images "${REGISTRY}/${service}" --format "table {{.Repository}}:{{.Tag}}" | tail -n +2 | tail -n +4)
        if [ ! -z "$old_images" ]; then
            echo "$old_images" | xargs -r docker rmi 2>/dev/null || true
        fi
    done
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [VERSION] [OPTIONS]"
    echo ""
    echo "Arguments:"
    echo "  VERSION     Docker image version tag (default: latest)"
    echo ""
    echo "Options:"
    echo "  --no-cache  Build without using cache"
    echo "  --push      Push images to registry after building"
    echo "  --cleanup   Clean up old images after building"
    echo "  --help      Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0                    # Build with latest tag"
    echo "  $0 v1.0.0            # Build with v1.0.0 tag"
    echo "  $0 latest --push     # Build and push to registry"
    echo "  $0 v1.0.0 --no-cache --cleanup  # Build without cache and cleanup"
}

# Parse command line arguments
PUSH_IMAGES=false
CLEANUP_AFTER=false

while [[ $# -gt 0 ]]; do
    case $1 in
        --no-cache)
            BUILD_ARGS="--no-cache"
            shift
            ;;
        --push)
            PUSH_IMAGES=true
            shift
            ;;
        --cleanup)
            CLEANUP_AFTER=true
            shift
            ;;
        --help)
            show_usage
            exit 0
            ;;
        -*)
            print_error "Unknown option $1"
            show_usage
            exit 1
            ;;
        *)
            if [ -z "$VERSION_SET" ]; then
                VERSION=$1
                VERSION_SET=true
            else
                print_error "Too many arguments"
                show_usage
                exit 1
            fi
            shift
            ;;
    esac
done

# Main execution
main() {
    print_status "Starting Docker image build process..."
    print_status "Registry: ${REGISTRY}"
    print_status "Version: ${VERSION}"
    
    # Check prerequisites
    check_docker
    
    # Build Maven project first
    print_status "Building Maven project..."
    if ./mvnw clean package -DskipTests; then
        print_success "Maven build completed successfully"
    else
        print_error "Maven build failed"
        exit 1
    fi
    
    # Build Docker images
    print_status "Building Docker images..."
    
    # Infrastructure services
    build_image "service-registry" "Dockerfiles/service-registry.Dockerfile"
    build_image "config-server" "Dockerfiles/config-server.Dockerfile"
    build_image "api-gateway" "Dockerfiles/api-gateway.Dockerfile"
    
    # Core services
    build_image "user-service" "Dockerfiles/user-service.Dockerfile"
    build_image "vendor-service" "Dockerfiles/vendor-service.Dockerfile"
    build_image "search-service" "Dockerfiles/search-service.Dockerfile"
    build_image "booking-service" "Dockerfiles/booking-service.Dockerfile"
    build_image "payment-service" "Dockerfiles/payment-service.Dockerfile"
    
    # Additional services (if Dockerfiles exist)
    if [ -f "Dockerfiles/admin-service.Dockerfile" ]; then
        build_image "admin-service" "Dockerfiles/admin-service.Dockerfile"
    fi
    
    if [ -f "Dockerfiles/analytics-service.Dockerfile" ]; then
        build_image "analytics-service" "Dockerfiles/analytics-service.Dockerfile"
    fi
    
    if [ -f "Dockerfiles/messaging-service.Dockerfile" ]; then
        build_image "messaging-service" "Dockerfiles/messaging-service.Dockerfile"
    fi
    
    # Push images if requested
    if [ "$PUSH_IMAGES" = true ]; then
        print_status "Pushing images to registry..."
        for service in service-registry config-server api-gateway user-service vendor-service search-service booking-service payment-service; do
            docker push "${REGISTRY}/${service}:${VERSION}"
            if [ "${VERSION}" != "latest" ]; then
                docker push "${REGISTRY}/${service}:latest"
            fi
        done
        print_success "All images pushed successfully"
    fi
    
    # Cleanup if requested
    if [ "$CLEANUP_AFTER" = true ]; then
        cleanup_images
        print_success "Cleanup completed"
    fi
    
    # Show summary
    print_success "All Docker images built successfully!"
    print_status "Built images:"
    docker images "${REGISTRY}/*" --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}\t{{.CreatedAt}}"
    
    print_status "To run the services:"
    echo "  docker-compose -f docker-compose.dev.yml up -d"
    echo ""
    print_status "To deploy to Kubernetes:"
    echo "  kubectl apply -f k8s/"
}

# Run main function
main "$@"
