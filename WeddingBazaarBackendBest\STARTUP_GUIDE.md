# Wedding Bazaar Backend - Startup Guide

This guide will help you run the Wedding Bazaar Backend microservices application.

## 🚀 Quick Start

### Prerequisites

1. **Java 17 or higher**
   ```bash
   java -version
   ```

2. **Maven 3.8+**
   ```bash
   mvn -version
   ```

3. **PostgreSQL** (required)
4. **Redis** (optional but recommended)

### Option 1: Quick Start (Recommended)

#### For Linux/Mac:
```bash
./quick-start.sh
```

#### For Windows:
```batch
run-application.bat
```

### Option 2: Manual Setup

#### Step 1: Start Infrastructure

**With Docker (Recommended):**
```bash
# Start PostgreSQL
docker run -d --name postgres-wedding \
  -e POSTGRES_DB=wedding_bazaar \
  -e POSTGRES_USER=postgres \
  -e POSTGRES_PASSWORD=postgres \
  -p 5432:5432 postgres:15

# Start Redis (optional)
docker run -d --name redis-wedding \
  -p 6379:6379 redis:7-alpine
```

**Manual Installation:**
- Install PostgreSQL and create database `wedding_bazaar`
- Install Redis (optional)

#### Step 2: Build the Project
```bash
mvn clean install -DskipTests
```

#### Step 3: Start Services in Order

**1. Service Registry (Eureka)**
```bash
cd service-registry
mvn spring-boot:run -Dspring.profiles.active=dev
```
Wait for startup, then open new terminal.

**2. Config Server**
```bash
cd config-server
mvn spring-boot:run -Dspring.profiles.active=dev
```
Wait for startup, then open new terminal.

**3. API Gateway**
```bash
cd api-gateway
mvn spring-boot:run -Dspring.profiles.active=dev
```
Wait for startup, then open new terminal.

**4. User Service**
```bash
cd user-service
mvn spring-boot:run -Dspring.profiles.active=dev
```

## 📊 Service Endpoints

Once all services are running:

| Service | Port | Health Check | Dashboard |
|---------|------|--------------|-----------|
| Service Registry | 8761 | http://localhost:8761/actuator/health | http://localhost:8761 |
| Config Server | 8888 | http://localhost:8888/actuator/health | - |
| API Gateway | 8080 | http://localhost:8080/actuator/health | http://localhost:8080/swagger-ui.html |
| User Service | 8081 | http://localhost:8081/actuator/health | - |
| Vendor Service | 8082 | http://localhost:8082/actuator/health | - |
| Search Service | 8083 | http://localhost:8083/actuator/health | - |
| Booking Service | 8084 | http://localhost:8084/actuator/health | - |
| Payment Service | 8085 | http://localhost:8085/actuator/health | - |

## 🔧 Configuration

### Environment Variables

```bash
export SPRING_PROFILES_ACTIVE=dev
export LOG_LEVEL=INFO
export JAVA_OPTS="-Xmx1g -Xms512m"
```

### Database Configuration

Default configuration expects:
- **Host:** localhost:5432
- **Database:** wedding_bazaar
- **Username:** postgres
- **Password:** postgres

### Redis Configuration (Optional)

Default configuration:
- **Host:** localhost:6379
- **No authentication**

## 🐛 Troubleshooting

### Common Issues

**1. Port Already in Use**
```bash
# Check what's using the port
netstat -tulpn | grep :8080

# Kill the process
kill -9 <PID>
```

**2. Database Connection Failed**
- Ensure PostgreSQL is running
- Check database credentials
- Verify database `wedding_bazaar` exists

**3. Service Won't Start**
- Check logs in `logs/` directory
- Ensure previous service is healthy before starting next
- Verify Java and Maven versions

**4. Out of Memory**
```bash
# Increase heap size
export JAVA_OPTS="-Xmx2g -Xms1g"
```

### Log Files

Service logs are stored in:
```
logs/
├── service-registry.log
├── config-server.log
├── api-gateway.log
├── user-service.log
└── ...
```

### Health Checks

Check if services are healthy:
```bash
# All services
curl http://localhost:8080/actuator/health

# Individual service
curl http://localhost:8081/actuator/health
```

## 🧪 Testing the Application

### 1. Check Service Registry
Visit: http://localhost:8761

You should see all registered services.

### 2. Test API Gateway
```bash
curl http://localhost:8080/actuator/health
```

### 3. Test User Service (via Gateway)
```bash
# Register a new user
curl -X POST http://localhost:8080/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "Test123!@#",
    "firstName": "John",
    "lastName": "Doe"
  }'
```

### 4. Access Swagger UI
Visit: http://localhost:8080/swagger-ui.html

## 🛑 Stopping the Application

### Quick Stop
```bash
./quick-start.sh stop
```

### Manual Stop
Kill each Java process or press `Ctrl+C` in each terminal.

### Stop Docker Infrastructure
```bash
docker stop postgres-wedding redis-wedding
docker rm postgres-wedding redis-wedding
```

## 🔄 Development Mode

For development with auto-reload:

```bash
cd user-service
mvn spring-boot:run -Dspring-boot.run.jvmArguments="-Dspring.devtools.restart.enabled=true"
```

## 📈 Monitoring

### Actuator Endpoints

Each service exposes monitoring endpoints:
- `/actuator/health` - Health status
- `/actuator/info` - Application info
- `/actuator/metrics` - Metrics
- `/actuator/env` - Environment properties

### Example Monitoring Commands

```bash
# Check all service health
for port in 8761 8888 8080 8081 8082 8083 8084 8085; do
  echo "Port $port: $(curl -s http://localhost:$port/actuator/health | jq -r '.status')"
done

# Check memory usage
curl -s http://localhost:8080/actuator/metrics/jvm.memory.used | jq
```

## 🚀 Production Deployment

For production deployment, see:
- `docker-compose.yml` for containerized deployment
- `k8s/` directory for Kubernetes manifests
- `build-images.sh` for building Docker images

## 📞 Support

If you encounter issues:

1. Check the logs in `logs/` directory
2. Verify all prerequisites are installed
3. Ensure infrastructure services are running
4. Check port availability
5. Review the troubleshooting section above

For additional help, check the main README.md file.
